import React, {
  createContext,
  memo,
  useContext,
  useEffect,
  useMemo,
} from "react";

import { MapStoreProvider } from "../contexts/map-store-context";
import { useMap } from "../hooks/use-map";
import { useMapActions } from "../hooks/use-map-actions";
import { cn } from "../lib/utils";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

interface SplitMode {
  count: 1 | 2 | 3 | 4;
}

interface MainMapProps extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;
}

interface SplitMapProps extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  splitMode?: SplitMode;
  onMapInit?: (mapState: UseMapReturn) => void;
}

interface MapContainerProps extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  splitMode?: SplitMode;
  onMapInit?: (mapState: UseMapReturn) => void;
}

/**
 * MainMap 컴포넌트 (Components 레이어)
 * 전역 MapProvider에서 제공하는 store를 사용하는 단일 지도 컴포넌트
 *
 * @example
 * ```tsx
 * <MainMap className="w-full h-96" center={[127, 37]}>
 *   <DrawProvider />
 * </MainMap>
 * ```
 */

// 분할 지도 위치 정보
type Position = "top-left" | "top-right" | "bottom-left" | "bottom-right";

// 분할 그리드 설정 함수
const getGridConfig = (count: number) => {
  switch (count) {
    case 1:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
    case 2:
      return {
        gridClass: "grid-cols-2 grid-rows-1",
        positions: ["top-left", "top-right"] as Position[],
      };
    case 3:
      return {
        gridClass: "grid-cols-3 grid-rows-1",
        positions: ["top-left", "top-right", "bottom-left"] as Position[],
      };
    case 4:
      return {
        gridClass: "grid-cols-2 grid-rows-2",
        positions: [
          "top-left",
          "top-right",
          "bottom-left",
          "bottom-right",
        ] as Position[],
      };
    default:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
  }
};

// 개별 지도 뷰 컴포넌트
interface MapViewProps extends Partial<MapInitializeOptions> {
  position: Position;
  onMapInit?: (mapState: UseMapReturn) => void;
  children?: React.ReactNode;
  showPositionLabel?: boolean; // 위치 라벨 표시 여부
}

const MapView = memo(
  ({
    onMapInit,
    children,
    center,
    zoom,
    projection,
    ...mapInitializeOptions
  }: MapViewProps) => {
    const id = React.useId();
    const containerRef = React.useRef<HTMLDivElement>(null);
    const initializedRef = React.useRef(false);

    const { isLoading, error } = useMap({
      containerRef,
      center,
      zoom,
      projection,
      ...mapInitializeOptions,
      onMapInit,
    });

    const { setCenter, setZoom } = useMapActions();

    useEffect(() => {
      if (!isLoading && !error && !initializedRef.current) {
        initializedRef.current = true;
        if (center && setCenter) {
          setCenter(center);
        }
        if (zoom !== undefined && setZoom) {
          setZoom(zoom);
        }
      }
    }, [isLoading, error, center, zoom, setCenter, setZoom]);

    if (error) {
      return (
        <div className="relative h-full w-full flex items-center justify-center">
          <div className="text-red-500">지도 로딩 실패: {error}</div>
        </div>
      );
    }

    return (
      <div className="relative h-full w-full">
        <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
        {!isLoading && <>{children}</>}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
            <div className="text-gray-600">지도 로딩 중...</div>
          </div>
        )}
      </div>
    );
  },
);

MapView.displayName = "MapView";

// MapPool 타입 정의
interface MapInstance {
  id: string;
  index: number;
  position: Position;
}

interface MapPoolContextType {
  mapInstances: MapInstance[];
}

// MapPool Context 생성
const MapPoolContext = createContext<MapPoolContextType | null>(null);

// MapPool Provider 컴포넌트
export const MapPoolProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // useMemo로 MapPool 인스턴스 안정화 - 컴포넌트 재렌더링 시에도 동일한 인스턴스 유지
  const mapInstances = useMemo(() => {
    const stableId = `map_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const positions: Position[] = [
      "top-left",
      "top-right",
      "bottom-left",
      "bottom-right",
    ];

    return Array.from({ length: 4 }, (_, index) => ({
      id: `${stableId}-${index}`,
      index,
      position: positions[index]!,
    }));
  }, []);

  const value = useMemo(() => ({ mapInstances }), [mapInstances]);

  return (
    <MapPoolContext.Provider value={value}>{children}</MapPoolContext.Provider>
  );
};

// MapPool Context 사용 훅
const useMapPoolContext = (): MapPoolContextType => {
  const context = useContext(MapPoolContext);
  if (!context) {
    throw new Error(
      "useMapPoolContext must be used within MapPoolProvider. " +
        "Map 컴포넌트는 자동으로 MapPoolProvider를 제공하므로 " +
        "이 오류가 발생하면 Map 컴포넌트 외부에서 useMapPoolContext를 사용했을 가능성이 있습니다.",
    );
  }
  return context;
};

/**
 * MainMap 컴포넌트 - 전역 MapProvider의 store를 사용하는 단일 지도
 */
export const MainMap = memo(
  ({
    className,
    style,
    children,
    onMapInit,
    center,
    zoom,
    projection,
    containerRef,
    ...mapInitializeOptions
  }: MainMapProps) => {
    const id = React.useId();
    const internalContainerRef = React.useRef<HTMLDivElement>(null);
    const finalContainerRef = containerRef || internalContainerRef;
    const initializedRef = React.useRef(false);

    const { isLoading, error } = useMap({
      containerRef: finalContainerRef,
      center,
      zoom,
      projection,
      ...mapInitializeOptions,
      onMapInit,
    });

    const { setCenter, setZoom } = useMapActions();

    useEffect(() => {
      if (!isLoading && !error && !initializedRef.current) {
        initializedRef.current = true;
        if (center && setCenter) {
          setCenter(center);
        }
        if (zoom !== undefined && setZoom) {
          setZoom(zoom);
        }
      }
    }, [isLoading, error, center, zoom, setCenter, setZoom]);

    if (error) {
      return (
        <div className="relative h-full w-full flex items-center justify-center">
          <div className="text-red-500">지도 로딩 실패: {error}</div>
        </div>
      );
    }

    return (
      <div className={cn("relative", className)} style={style}>
        <div
          id={`map-${id}`}
          ref={finalContainerRef}
          className="w-full h-full"
        />
        {!isLoading && <>{children}</>}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
            <div className="text-gray-600">지도 로딩 중...</div>
          </div>
        )}
      </div>
    );
  },
);

MainMap.displayName = "MainMap";

/**
 * SplitMap 컴포넌트 - 다중 지도 분할 컴포넌트, 각 인스턴스마다 독립적인 MapStoreProvider 사용
 */
const SplitMapComponent = memo(
  ({
    className,
    style,
    children,
    onMapInit,
    center,
    zoom,
    projection,
    splitMode = { count: 1 },
    ...mapInitializeOptions
  }: SplitMapProps) => {
    const { mapInstances } = useMapPoolContext();

    // 현재 분할 모드에 따른 그리드 설정
    const { gridClass, positions } = getGridConfig(splitMode.count);

    return (
      <div className={cn("relative", className)} style={style}>
        <div className={`grid ${gridClass} gap-1 h-full w-full`}>
          {mapInstances.slice(0, splitMode.count).map((mapInstance, index) => {
            const actualPosition = positions[index] ?? mapInstance.position;

            if (!actualPosition) {
              console.error(`Invalid position for map ${index}`);
              return null;
            }

            return (
              <div
                key={mapInstance.id}
                className={
                  splitMode.count === 1
                    ? "relative"
                    : "relative border border-gray-300"
                }
              >
                <MapStoreProvider mapId={mapInstance.id}>
                  <MapView
                    center={center}
                    zoom={zoom}
                    projection={projection}
                    position={actualPosition}
                    onMapInit={onMapInit}
                    showPositionLabel={splitMode.count > 1}
                    {...mapInitializeOptions}
                  >
                    {children}
                  </MapView>
                </MapStoreProvider>
              </div>
            );
          })}
        </div>
      </div>
    );
  },
);

SplitMapComponent.displayName = "SplitMapComponent";

/**
 * SplitMap - 분할 지도를 위한 컴포넌트
 */
export const SplitMap: React.FC<SplitMapProps> = (props) => {
  return (
    <MapPoolProvider>
      <SplitMapComponent {...props} />
    </MapPoolProvider>
  );
};

/**
 * MapContainer 컴포넌트 - MainMap 고정 + 추가 독립 뷰들로 분할 구성
 * MainMap은 항상 첫 번째 셀에 고정되어 상태 보존, 나머지만 동적 생성/제거
 *
 * @example
 * ```tsx
 * // 기본 단일 지도
 * <MapContainer />
 *
 * // 2분할 지도 (MainMap + 독립뷰 1개)
 * <MapContainer splitMode={{ count: 2 }} />
 * ```
 */
/**
 * MapContainer 내부의 추가 뷰들을 위한 SplitMap 래퍼 컴포넌트
 * MapPool 인스턴스를 재활용하여 효율적인 지도 관리
 */
const AdditionalMapsContainer = memo(
  ({
    splitMode,
    children,
    ...props
  }: {
    splitMode: SplitMode;
    children?: React.ReactNode;
  } & Partial<MapInitializeOptions>) => {
    const { mapInstances } = useMapPoolContext();

    // 추가로 필요한 인스턴스들 (MainMap 제외)
    const additionalInstances = mapInstances.slice(1, splitMode.count);

    return (
      <>
        {additionalInstances.map((mapInstance) => (
          <div key={mapInstance.id} className="relative border border-gray-300">
            <MapStoreProvider mapId={mapInstance.id}>
              <MapView position={mapInstance.position} {...props}>
                {children}
              </MapView>
            </MapStoreProvider>
          </div>
        ))}
      </>
    );
  },
);

AdditionalMapsContainer.displayName = "AdditionalMapsContainer";

export const MapContainer = memo(
  ({
    splitMode = { count: 1 },
    className,
    style,
    children,
    ...props
  }: MapContainerProps) => {
    const { gridClass } = getGridConfig(splitMode.count);

    return (
      <div className={cn("relative", className)} style={style}>
        <MapPoolProvider>
          <div className={`grid ${gridClass} gap-1 h-full w-full`}>
            {/* MainMap은 항상 첫 번째 셀에 렌더링 (조건문 없음) */}
            <div
              className={
                splitMode.count === 1
                  ? "relative"
                  : "relative border border-gray-300"
              }
            >
              <MainMap className="h-full w-full" {...props}>
                {children}
              </MainMap>
            </div>

            {/* count >= 2일 때만 추가 뷰들 동적 생성 */}
            <AdditionalMapsContainer splitMode={splitMode} {...props}>
              {children}
            </AdditionalMapsContainer>
          </div>
        </MapPoolProvider>
      </div>
    );
  },
);

MapContainer.displayName = "MapContainer";

/**
 * 기본 Map 컴포넌트 - MainMap과 동일 (하위 호환성)
 */
export const Map = MainMap;
