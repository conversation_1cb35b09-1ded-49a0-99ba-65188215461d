import { useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import { HomeIcon } from "lucide-react";
import React, { useMemo } from "react";

import { MoveMapHomeContext, useMoveMapHomeContext } from "../../contexts";
import {
  MoveMapHomeProps,
  MoveMapHomeTriggerProps,
  MoveMapHomeWidgetProps,
} from "../../types/move-map-home-types";

export const MoveMapHomeTrigger = React.forwardRef<
  HTMLButtonElement,
  MoveMapHomeTriggerProps
>(({ className, children, ...props }, ref) => {
  const { zoom, center, animate } = useMoveMapHomeContext();
  return (
    <Button
      ref={ref}
      className={cn(
        "cursor-pointer text-black bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800",
        className,
      )}
      onClick={() => {
        animate({
          zoom: zoom,
          center: center,
          duration: 500,
        });
      }}
      {...props}
    >
      {children}
    </Button>
  );
});
MoveMapHomeTrigger.displayName = "MoveMapHomeTrigger";

export const MoveMapHome = React.forwardRef<HTMLDivElement, MoveMapHomeProps>(
  ({ zoom, center, duration, className, children, ...props }, ref) => {
    const { map } = useMap();
    const animate = map.getView().animate.bind(map.getView());

    const contextValue = useMemo(
      () => ({
        zoom,
        center,
        duration,
        animate,
      }),
      [zoom, center, duration, animate],
    );
    return (
      <div
        ref={ref}
        className={
          (cn("absolute right-4 top-35 flex flex-col gap-2"), className)
        }
        {...props}
      >
        <MoveMapHomeContext.Provider value={contextValue}>
          {children}
        </MoveMapHomeContext.Provider>
      </div>
    );
  },
);
MoveMapHome.displayName = "MoveMapHome";

export const MoveMapHomeWidget = ({
  zoom,
  center,
  duration,
  className,
}: MoveMapHomeWidgetProps) => {
  return (
    <MoveMapHome
      zoom={zoom}
      center={center}
      duration={duration}
      className={className}
    >
      <MoveMapHomeTrigger className="">
        <HomeIcon className="w-5 h-5" />
      </MoveMapHomeTrigger>
    </MoveMapHome>
  );
};
