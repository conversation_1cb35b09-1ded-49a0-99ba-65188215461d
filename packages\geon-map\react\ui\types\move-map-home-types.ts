// Map widget setting
export interface MoveMapHomeSetting {
  center: [number, number];
  zoom: number;
  duration?: number;
}

// 전체 Context Value 인터페이스
export interface MoveMapHomeContextValue extends MoveMapHomeSetting {
  animate: ({
    zoom,
    center,
    duration,
  }: {
    zoom: number;
    center: [number, number];
    duration: number;
  }) => void;
}
// 컴포넌트 Props 인터페이스들
export interface MoveMapHomeWidgetProps extends MoveMapHomeProps {}

export interface MoveMapHomeProps
  extends React.ComponentPropsWithoutRef<"div">,
    MoveMapHomeSetting {}

export interface MoveMapHomeTriggerProps
  extends React.ComponentPropsWithoutRef<"button"> {}
