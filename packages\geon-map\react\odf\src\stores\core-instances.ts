import type {
  DrawControlOptions,
  MeasureControlOptions,
  ODF,
  ODF_MAP,
} from "@geon-map/core";
import {
  Basemap,
  Clear,
  Draw,
  Event,
  LayerFactory,
  Map,
  Measure,
  Overview,
  Scale,
} from "@geon-map/core";

/**
 * 🎯 Core 인스턴스 생성/관리 전담
 * Store와 관련된 모든 Core 인스턴스 로직을 통합 관리
 */
export class CoreInstanceManager {
  /**
   * 🗺️ 기본 Map Core 인스턴스들 생성 (필수 Core만)
   * MapProvider에서 기본적으로 필요한 Core들만 생성
   * Scale, Basemap은 개별 Provider에서 초기화
   */
  static createMapCores(
    map: ODF_MAP,
    odf: ODF,
  ): {
    eventInstance: Event | null;
    mapInstance: Map | null;
    layerFactory: LayerFactory | null;
    errors: string[];
  } {
    console.log("🗺️ CoreInstanceManager: Creating essential map cores...");

    let eventInstance = null;
    let mapInstance = null;
    let layerFactory = null;
    const errors: string[] = [];

    // 1. Event Core (필수 - 이벤트 처리용)
    try {
      eventInstance = new Event(map, odf);
      console.log("✅ Event core created");
    } catch (error) {
      const errMsg = `Event core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    // 2. Map Controller (필수 - 지도 조작용)
    try {
      mapInstance = new Map(map);
      console.log("✅ Map core created");
    } catch (error) {
      const errMsg = `Map creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    // 3. LayerFactory (필수 - 레이어 생성용)
    try {
      layerFactory = new LayerFactory(map, odf);
      console.log("✅ LayerFactory created");
    } catch (error) {
      const errMsg = `LayerFactory creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      eventInstance,
      mapInstance,
      layerFactory,
      errors,
    };
  }
  /**
   * 🎨 Draw 관련 Core 인스턴스들 생성
   */
  static async createDrawCores(
    map: ODF_MAP,
    odf: ODF,
    drawOptions: DrawControlOptions,
    measureOptions?: MeasureControlOptions,
  ): Promise<{
    drawCore: Draw | null;
    measureCore: Measure | null;
    clearCore: Clear | null;
    drawControl: any | null;
    measureControl: any | null;
    clearControl: any | null;
    errors: string[];
  }> {
    console.log("🎨 CoreInstanceManager: Creating draw-related cores...");

    let drawCore = null;
    let drawControl = null;
    let measureCore = null;
    let measureControl = null;
    let clearCore = null;
    let clearControl = null;
    const errors: string[] = [];

    // 1. Draw Core (필수) - 생성자 자동 초기화
    try {
      drawCore = new Draw(map, odf, drawOptions);
      drawControl = drawCore.getDrawControl();
      console.log("✅ Draw core created");
    } catch (error) {
      const errMsg = `Draw core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    // 2. Measure Core (선택적) - 생성자 자동 초기화
    try {
      measureCore = new Measure(map, measureOptions || {});
      measureControl = measureCore.getMeasureControl();
      console.log("✅ Measure core created");
    } catch (error) {
      const errMsg = `Measure core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("⚠️", errMsg);
    }

    // 3. Clear Core (선택적) - 생성자 자동 초기화
    try {
      clearCore = new Clear(map, odf, true);
      clearControl = clearCore.getClearControl();
      console.log("✅ Clear core created");
    } catch (error) {
      const errMsg = `Clear core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("⚠️", errMsg);
    }

    return {
      drawCore,
      measureCore,
      clearCore,
      drawControl,
      measureControl,
      clearControl,
      errors,
    };
  }

  /**
   * 🧹 모든 Core 인스턴스 정리
   */
  static cleanupDrawCores(cores: {
    drawCore: Draw | null;
    measureCore: Measure | null;
    clearCore: Clear | null;
  }): void {
    const { drawCore, measureCore, clearCore } = cores;

    // Draw Core 정리
    if (drawCore) {
      try {
        drawCore.destroy();
        console.log("✅ Draw core cleaned up");
      } catch (error) {
        console.error("Failed to cleanup Draw core:", error);
      }
    }

    // Measure Core 정리
    if (measureCore) {
      try {
        measureCore.destroy();
        console.log("✅ Measure core cleaned up");
      } catch (error) {
        console.error("Failed to cleanup Measure core:", error);
      }
    }

    // Clear Core 정리
    if (clearCore) {
      try {
        clearCore.destroy();
        console.log("✅ Clear core cleaned up");
      } catch (error) {
        console.error("Failed to cleanup Clear core:", error);
      }
    }

    // Event Core는 destroy 메서드가 없음 (단순 이벤트 래퍼)
    console.log("✅ Event core cleaned up (no destroy method needed)");
  }

  /**
   * 🔍 Core 상태 검증
   */
  static validateDrawCores(cores: { drawCore: Draw | null }): {
    isValid: boolean;
    error?: Error;
  } {
    const { drawCore } = cores;

    // Draw Core는 필수
    if (!drawCore) {
      return {
        isValid: false,
        error: new Error("Draw core is required but failed to initialize"),
      };
    }

    return { isValid: true };
  }

  /**
   * 🎯 Scale Core 인스턴스 생성 (개별 Provider용)
   */
  static createScaleCores(
    map: ODF_MAP,
    odf: ODF,
    scaleOptions?: { size?: number; scaleInput?: boolean },
  ): {
    scaleInstance: Scale | null;
    errors: string[];
  } {
    console.log("🎯 CoreInstanceManager: Creating scale core...");

    let scaleInstance = null;
    const errors: string[] = [];

    try {
      scaleInstance = new Scale(map, odf);
      if (
        scaleOptions &&
        scaleOptions.size !== undefined &&
        scaleOptions.scaleInput !== undefined
      ) {
        scaleInstance.setControl({
          size: scaleOptions.size,
          scaleInput: scaleOptions.scaleInput,
        });
      }
      console.log("✅ Scale Core created");
    } catch (error) {
      const errMsg = `Scale Core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      scaleInstance,
      errors,
    };
  }

  /**
   * 🗺️ Basemap Core 인스턴스 생성 (개별 Provider용)
   */
  static createBasemapCores(
    map: ODF_MAP,
    odf: ODF,
    _basemapOptions?: { basemapList?: any; urls?: any },
  ): {
    basemapInstance: Basemap | null;
    errors: string[];
  } {
    console.log(
      "🗺️ CoreInstanceManager: Creating basemap core...",
      _basemapOptions,
    );

    let basemapInstance = null;
    const errors: string[] = [];

    try {
      basemapInstance = new Basemap(map, odf);
      // TODO: basemapOptions 활용 로직 추가 필요
      console.log("✅ Basemap Core created");
    } catch (error) {
      const errMsg = `Basemap Core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      basemapInstance,
      errors,
    };
  }

  /**
   * 🎨 Overview Core 생성 (개별 Provider용)
   */
  static createOverviewCore(
    map: ODF_MAP,
    odf: ODF,
  ): {
    overviewCore: Overview | null;
    errors: string[];
  } {
    let overviewCore = null;
    const errors: string[] = [];

    try {
      overviewCore = new Overview(map, odf);
      console.log("✅ overviewCore created");
    } catch (error) {
      const errMsg = `overviewCore creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }
    return {
      overviewCore,
      errors,
    };
  }

  /**
   * 🎨 Draw Core 생성 (개별 Provider용)
   */
  static createDrawCore(
    map: ODF_MAP,
    odf: ODF,
    drawOptions?: any,
  ): {
    drawCore: Draw | null;
    errors: string[];
  } {
    console.log("🎨 CoreInstanceManager: Creating draw core...");

    let drawCore = null;
    const errors: string[] = [];

    try {
      drawCore = new Draw(map, odf, drawOptions);
      console.log("✅ Draw Core created");
    } catch (error) {
      const errMsg = `Draw Core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      drawCore,
      errors,
    };
  }

  /**
   * 📏 Measure Core만 생성 (개별 Provider용)
   */
  static createMeasureOnlyCores(
    map: ODF_MAP,
    _odf: ODF,
    measureOptions?: MeasureControlOptions,
  ): {
    measureCore: Measure | null;
    errors: string[];
  } {
    console.log("📏 CoreInstanceManager: Creating measure core...");

    let measureCore = null;
    const errors: string[] = [];

    try {
      measureCore = new Measure(map, measureOptions);
      console.log("✅ Measure Core created");
    } catch (error) {
      const errMsg = `Measure Core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      measureCore,
      errors,
    };
  }

  /**
   * 🧹 Clear Core만 생성 (개별 Provider용)
   */
  static createClearOnlyCores(
    map: ODF_MAP,
    odf: ODF,
    clearAll: boolean = true,
  ): {
    clearCore: Clear | null;
    errors: string[];
  } {
    console.log("🧹 CoreInstanceManager: Creating clear-only core...");

    let clearCore = null;
    const errors: string[] = [];

    try {
      clearCore = new Clear(map, odf, clearAll);
      console.log("✅ Clear Core created");
    } catch (error) {
      const errMsg = `Clear Core creation failed: ${error}`;
      errors.push(errMsg);
      console.error("❌", errMsg);
    }

    return {
      clearCore,
      errors,
    };
  }
}
