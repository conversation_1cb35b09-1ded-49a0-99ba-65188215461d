{"permissions": {"allow": ["Read(/C:\\magp\\magp-turbo\\apps\\web\\components\\dynamic-search/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\components\\dynamic-search/**)", "Write(/C:\\magp\\magp-turbo\\apps\\web/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\components\\dynamic-search/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\components\\dynamic-search/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\app\\service\\_contexts/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\app\\service\\_components\\sidebar/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\app\\service\\_utils/**)", "Write(/C:\\magp\\magp-turbo\\apps\\web/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web\\app\\service\\_contexts/**)", "Read(/C:\\magp\\magp-turbo\\packages\\geon-map\\react\\odf\\src\\hooks/**)", "Read(/C:\\magp\\magp-turbo\\packages\\geon-map\\react\\odf\\src\\hooks/**)", "Read(/C:\\magp\\magp-turbo\\packages\\geon-map\\react\\odf\\src/**)", "Read(/C:\\magp\\magp-turbo\\packages\\geon-map\\react\\odf\\src\\hooks/**)", "Read(/C:\\magp\\magp-turbo\\packages\\geon-map\\react\\odf\\src\\providers/**)", "Read(/C:\\magp\\magp-turbo\\apps\\web/**)", "mcp__ide__getDiagnostics", "WebSearch", "WebFetch(domain:medium.com)", "WebFetch(domain:codesandbox.io)"], "deny": [], "ask": []}}