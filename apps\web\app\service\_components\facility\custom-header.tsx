import { Button } from "@geon-ui/react/primitives/button";
import { Badge } from "@geon-ui/react/primitives/badge";
import {
  Plus,
  FileSpreadsheet,
  Edit3,
  Trash2,
  BarChart3,
  MapPin
} from "lucide-react";
import { useState } from "react";

export function CustomHeader() {
  // const { selectedFacilities } = useFacilitySearch();
  const [selectedCount] = useState(0); // 실제로는 선택된 시설물 수
  const [totalCount] = useState(1247); // 실제로는 전체 시설물 수

  const handleRegisterFacility = () => {
    // TODO: 시설물 등록 모달 열기
    console.log("시설물 등록");
  };

  const handleExcelDownload = () => {
    // TODO: 전체 결과 엑셀 다운로드 API 호출
    console.log("엑셀 다운로드");
  };

  const handleEditMode = () => {
    // TODO: 편집 모드 전환
    console.log("편집 모드");
  };

  const handleDeleteSelected = () => {
    // TODO: 선택된 시설물 삭제
    console.log("선택 삭제");
  };

  const handleStatistics = () => {
    // TODO: 통계 보기
    console.log("통계 보기");
  };

  return (
    <div className="border-b border-gray-200 py-2">
      <div className="flex items-center justify-end">

        {/* 우측: 액션 버튼들 */}
        <div className="flex items-center gap-2">
          {/* 통계 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleStatistics}
            className="text-gray-600 hover:text-gray-800"
          >
            <BarChart3 className="h-4 w-4 mr-1" />
            통계
          </Button>

          {/* 엑셀 다운로드 버튼 */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleExcelDownload}
            className="text-green-600 hover:text-green-800 hover:bg-green-50"
          >
            <FileSpreadsheet className="h-4 w-4 mr-1" />
            엑셀 다운로드
          </Button>

          {/* 선택 삭제 버튼 (선택된 항목이 있을 때만) */}
          {selectedCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteSelected}
              className="text-red-600 hover:text-red-800 hover:bg-red-50"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              삭제 ({selectedCount})
            </Button>
          )}

          {/* 시설물 등록 버튼 */}
          <Button
            size="sm"
            onClick={handleRegisterFacility}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-1" />
            등록
          </Button>
        </div>
      </div>
    </div>
  );
}
