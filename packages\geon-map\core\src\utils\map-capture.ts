// Konva를 동적으로 임포트 (SSR 방지)
let KonvaModule: any = null;

async function loadKonva() {
  if (typeof window === "undefined") {
    throw new Error("Konva can only be used in browser environment");
  }

  if (!KonvaModule) {
    KonvaModule = await import("konva");
  }

  return KonvaModule.default;
}

// html-to-image 동적 임포트
let htmlToImageModule: any = null;

async function loadHtmlToImage() {
  if (typeof window === "undefined") {
    throw new Error("html-to-image can only be used in browser environment");
  }

  if (!htmlToImageModule) {
    htmlToImageModule = await import("html-to-image");
  }

  return htmlToImageModule;
}

export interface MapCaptureConfig {
  fillColor?: string;
  strokeColor?: string;
  strokeWidth?: number;
  dashPattern?: number[];
  opacity?: number;
}

export interface CaptureRect {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface CaptureResult {
  canvas: HTMLCanvasElement;
  rect: CaptureRect; // CSS 좌표(px)
  rectPx: CaptureRect; // 실제 픽셀 좌표(px)
}

export class MapCapture {
  private mapTargetElement: HTMLElement | HTMLCanvasElement;
  private sourceCanvas: HTMLCanvasElement | null = null;
  private overlay: HTMLDivElement | null = null;
  private stage: any = null;
  private layer: any = null;
  private selection: any = null;
  private config: MapCaptureConfig;
  private origin: { x: number; y: number } | null = null;
  private isActive = false;
  private onCaptureCallback?: (result: CaptureResult) => void;
  private onInitializedCallback?: () => void;
  private Konva: any = null;

  constructor(
    mapTarget: HTMLElement | HTMLCanvasElement,
    config?: MapCaptureConfig,
  ) {
    this.mapTargetElement = mapTarget;
    this.sourceCanvas = mapTarget.querySelector("canvas");
    this.config = {
      fillColor: "rgba(0,123,255,0.15)",
      strokeColor: "#007bff",
      strokeWidth: 2,
      dashPattern: [5, 5],
      opacity: 1,
      ...config,
    };

    // init을 비동기로 처리하도록 변경
    this.initAsync();
  }

  private async initAsync(): Promise<void> {
    try {
      // 브라우저 환경에서만 실행
      if (typeof window === "undefined") {
        throw new Error("MapCapture can only be used in browser environment");
      }

      // Konva 동적 로드
      this.Konva = await loadKonva();

      // if (!this.sourceCanvas) {
      //   throw new Error("캔버스 요소를 찾을 수 없습니다.");
      // }

      this.setupOverlay();
      this.setupKonva();

      // 초기화 완료 콜백 호출
      this.onInitializedCallback?.();
    } catch (error) {
      console.error("MapCapture 초기화 실패:", error);
    }
  }

  /**
   * 선택 영역을 그릴 오버레이 DOM 생성 및 설정
   */
  private setupOverlay(): void {
    const target = this.mapTargetElement as HTMLElement;
    if (!target) throw new Error("No parent container for target");

    if (getComputedStyle(target).position === "static") {
      target.style.position = "relative";
    }

    this.overlay = document.createElement("div");
    Object.assign(this.overlay.style, {
      position: "absolute",
      left: "0",
      top: "0",
      width: `${target.clientWidth}px`,
      height: `${target.clientHeight}px`,
      pointerEvents: "none", // 기본은 none, startSelection에서 auto
      zIndex: "99999",
      userSelect: "none",
      touchAction: "none", // 모바일 제스처 간섭 방지
    });
    target.appendChild(this.overlay);

    // 크기 동기화
    const ro = new ResizeObserver(() => {
      if (!this.overlay || !this.stage) return;
      this.overlay.style.width = `${target.clientWidth}px`;
      this.overlay.style.height = `${target.clientHeight}px`;
      this.stage.width(target.clientWidth);
      this.stage.height(target.clientHeight);
      this.stage.batchDraw();
    });
    ro.observe(target);
  }

  /**
   * Konva Stage/Layer/Selection 객체 생성 및 이벤트 설정
   */
  private setupKonva(): void {
    if (!this.overlay || !this.Konva) return;

    // Konva Stage: 2D 캔버스 렌더링 엔진의 최상위 컨테이너
    // 타겟 요소와 동일한 크기의 캔버스 Stage 생성
    this.stage = new this.Konva.Stage({
      container: this.overlay,
      width: (this.mapTargetElement as HTMLElement).clientWidth,
      height: (this.mapTargetElement as HTMLElement).clientHeight,
    });

    // Layer: 실제 그림이 그려지는 캔버스 레이어 (도형들의 컨테이너)
    this.layer = new this.Konva.Layer();
    this.stage.add(this.layer);

    // 선택 영역을 표시할 사각형 (초기엔 숨김 상태)
    this.selection = new this.Konva.Rect({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      fill: this.config.fillColor,
      stroke: this.config.strokeColor,
      strokeWidth: this.config.strokeWidth,
      dash: this.config.dashPattern,
      opacity: this.config.opacity,
      visible: false,
      listening: false,
    });
    this.layer.add(this.selection);

    this.setupEvents();
  }

  private setupEvents(): void {
    if (!this.stage) return;

    // 드래그 시작
    this.stage.on("mousedown touchstart", () => {
      if (!this.isActive) return;
      const pointer = this.stage?.getPointerPosition(); // 마우스/터치 위치를 가져오는 메소드
      if (!pointer) return;

      this.origin = { x: pointer.x, y: pointer.y };

      // 선택 사각형 초기 설정
      this.selection?.position(this.origin);
      this.selection?.size({ width: 0, height: 0 });
      this.selection?.visible(true);

      // 실제 화면에 반영
      this.layer?.batchDraw();
    });

    // 드래그 중
    this.stage.on("mousemove touchmove", () => {
      if (!this.isActive || !this.origin) return;
      const pointer = this.stage?.getPointerPosition();
      if (!pointer) return;

      const x = Math.min(this.origin.x, pointer.x); // 시작점과 현재 좌표 중 더 작은 X값(왼쪽 경계)
      const y = Math.min(this.origin.y, pointer.y); // 시작점과 현재 좌표 중 더 작은 Y값(위쪽 경계)

      // 사각형의 크기 계산 (절댓값으로 항상 양수)
      const width = Math.abs(pointer.x - this.origin.x);
      const height = Math.abs(pointer.y - this.origin.y);

      this.selection?.position({ x, y });
      this.selection?.size({ width, height });
      this.layer?.batchDraw();
    });

    // 드래그 끝
    this.stage.on("mouseup touchend", () => {
      if (!this.isActive || !this.origin) return;
      this.origin = null;
      const result = this.captureSelectedMap();
      if (result && this.onCaptureCallback) {
        this.onCaptureCallback(result);
      }
    });
  }

  private captureSelectedMap(): CaptureResult | null {
    if (!this.selection || !this.sourceCanvas) return null;

    const rectCss: CaptureRect = {
      x: this.selection.x(),
      y: this.selection.y(),
      width: this.selection.width(),
      height: this.selection.height(),
    };
    //if (rectCss.width < 5 || rectCss.height < 5) return null;

    //배율 계산
    const scaleX = this.sourceCanvas.width / this.sourceCanvas.clientWidth;
    const scaleY = this.sourceCanvas.height / this.sourceCanvas.clientHeight;

    const clamp = (v: number, min: number, max: number) =>
      Math.max(min, Math.min(max, v));

    const sourceX = clamp(
      Math.round(rectCss.x * scaleX),
      0,
      this.sourceCanvas.width,
    );
    const sourceY = clamp(
      Math.round(rectCss.y * scaleY),
      0,
      this.sourceCanvas.height,
    );
    const sourceWidth = clamp(
      Math.round(rectCss.width * scaleX),
      0,
      this.sourceCanvas.width - sourceX,
    );
    const sourceHeight = clamp(
      Math.round(rectCss.height * scaleY),
      0,
      this.sourceCanvas.height - sourceY,
    );

    if (sourceWidth <= 0 || sourceHeight <= 0) return null;

    const outputCanvas = document.createElement("canvas");
    outputCanvas.width = sourceWidth;
    outputCanvas.height = sourceHeight;

    const ctx = outputCanvas.getContext("2d");
    if (!ctx) return null;

    ctx.drawImage(
      this.sourceCanvas,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      0,
      0,
      sourceWidth,
      sourceHeight,
    );

    return {
      canvas: outputCanvas,
      rect: rectCss,
      rectPx: {
        x: sourceX,
        y: sourceY,
        width: sourceWidth,
        height: sourceHeight,
      },
    };
  }

  /**
   * DataURL에서 CaptureResult 생성하는 헬퍼 메서드
   */
  private createCaptureResultFromDataUrl(
    dataUrl: string,
  ): Promise<CaptureResult> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;

        const ctx = canvas.getContext("2d");
        if (!ctx) {
          reject(new Error("Canvas context를 생성할 수 없습니다."));
          return;
        }

        ctx.drawImage(img, 0, 0);

        const target = this.mapTargetElement as HTMLElement;
        resolve({
          canvas,
          rect: {
            x: 0,
            y: 0,
            width: target.clientWidth,
            height: target.clientHeight,
          },
          rectPx: {
            x: 0,
            y: 0,
            width: canvas.width,
            height: canvas.height,
          },
        });
      };
      img.onerror = () => reject(new Error("이미지 로드 실패"));
      img.src = dataUrl;
    });
  }

  public startSelection(): void {
    this.isActive = true;
    if (this.overlay) {
      this.overlay.style.pointerEvents = "auto";
      this.overlay.style.cursor = "crosshair";
    }
  }

  public stopSelection(): void {
    this.isActive = false;
    if (this.overlay) {
      this.overlay.style.pointerEvents = "none";
      this.overlay.style.cursor = "default";
    }
  }

  public clearSelection(): void {
    if (!this.selection || !this.layer) return;
    this.origin = null;
    this.selection.position({ x: 0, y: 0 });
    this.selection.size({ width: 0, height: 0 });
    this.selection.visible(false);
    this.layer.batchDraw();
  }

  public onCapture(callback: (result: CaptureResult) => void): void {
    this.onCaptureCallback = callback;
  }

  // 초기화 완료 콜백 설정
  public onInitialized(callback: () => void): void {
    this.onInitializedCallback = callback;
  }

  /**
   * 현재 지도 화면 전체를 캡처 (html-to-image 사용)
   */
  public captureCurrentMap(): CaptureResult | null {
    if (!this.sourceCanvas) {
      console.error("지도 캔버스를 찾을 수 없습니다.");
      return null;
    }

    // 새 캔버스 생성
    const canvas = document.createElement("canvas");
    canvas.width = this.sourceCanvas.width;
    canvas.height = this.sourceCanvas.height;

    const ctx = canvas.getContext("2d");
    if (!ctx) {
      console.error("캔버스 context를 생성할 수 없습니다.");
      return null;
    }

    ctx.drawImage(this.sourceCanvas, 0, 0);

    const target = this.mapTargetElement as HTMLElement;
    return {
      canvas,
      rect: {
        x: 0,
        y: 0,
        width: target.clientWidth,
        height: target.clientHeight,
      },
      rectPx: {
        x: 0,
        y: 0,
        width: canvas.width,
        height: canvas.height,
      },
    };
  }

  /**
   * 타겟 요소 크기 변경에 따른 오버레이 크기 동기화
   * 브라우저 창 크기 변경, 반응형 레이아웃 등으로 타겟 요소 크기가 변경될 때 호출
   */
  public resize(): void {
    if (!this.stage) return;
    this.stage.width((this.mapTargetElement as HTMLElement).clientWidth);
    this.stage.height((this.mapTargetElement as HTMLElement).clientHeight);
    this.stage.batchDraw();
  }

  public destroy(): void {
    if (this.stage) {
      this.stage.destroy();
      this.stage = null;
    }
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
      this.overlay = null;
    }
    this.layer = null;
    this.selection = null;
    this.sourceCanvas = null;
    this.origin = null;
    this.isActive = false;
    this.onCaptureCallback = undefined;
    this.onInitializedCallback = undefined;
    this.Konva = null;
  }
}
