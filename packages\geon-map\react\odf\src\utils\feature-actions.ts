/**
 * 🎯 Feature 조작 유틸리티
 *
 * Core의 Feature 클래스 메서드를 사용하여 피처를 조작하고,
 * 필요시 React Store 상태를 동기화하는 헬퍼 함수들을 제공합니다.
 *
 * 핵심 원칙:
 * 1. ODF 객체에 직접 접근하지 않고 Core 메서드만 사용
 * 2. Store 업데이트 함수를 명시적으로 주입받아 의존성 명확화
 * 3. 순수 함수로 구현하여 테스트 용이성 확보
 */

import { Feature } from "@geon-map/core";

import { determineLayerTypeByODFId, getODFLayerId } from "../hooks/use-layer";
import type { Layer } from "../types/layer-types";

/**
 * ✅ 통합된 피처 삭제 + Store 동기화
 *
 * Layer 인터페이스를 사용하여 레이어 타입을 자동 판별하고,
 * 적절한 Store 업데이트를 수행합니다.
 *
 * @param layer - Layer 인터페이스 객체 (metadata + odfLayer)
 * @param feature - 삭제할 피처 객체
 * @param onStoreUpdate - Store 업데이트 함수 (featureId, layerType 전달)
 * @returns 삭제 성공 여부
 *
 * @example
 * ```typescript
 * const { drawLayer } = useLayer();
 * const success = deleteFeature(
 *   drawLayer,
 *   selectedFeature,
 *   (featureId, layerType) => {
 *     if (layerType === 'draw') {
 *       useDrawStore.getState().removeDrawnFeature(featureId);
 *     } else if (layerType === 'measure') {
 *       useDrawStore.getState().removeMeasureResult(featureId);
 *     }
 *   }
 * );
 * ```
 */
export function deleteFeature(
  layer: Layer | null,
  feature: any,
  onStoreUpdate?: (
    featureId: string,
    layerType: "draw" | "measure" | "clear" | "other",
  ) => void,
): boolean {
  if (!layer?.odfLayer || !feature) {
    console.warn("deleteFeature: layer 또는 feature가 없습니다.");
    return false;
  }

  try {
    // ✅ Core 메서드만 사용 (ODF 직접 접근 금지)
    const success = Feature.deleteFeature(layer.odfLayer, feature);

    if (success && onStoreUpdate) {
      // 피처 ID 추출 (안전한 방식)
      const featureId = extractFeatureId(feature);

      // 레이어 타입 자동 판별
      const odfLayerId = getODFLayerId(layer);
      const layerType = odfLayerId
        ? determineLayerTypeByODFId(odfLayerId)
        : (layer.type as "draw" | "measure" | "clear" | "other");

      onStoreUpdate(featureId, layerType);
    }

    return success;
  } catch (error) {
    console.error("deleteFeature 실패:", error);
    return false;
  }
}

/**
 * 피처 스타일 변경 + Store 동기화 (선택사항)
 *
 * @param feature - 스타일을 변경할 피처 객체
 * @param styleOptions - 새로운 스타일 옵션
 * @param onStoreUpdate - Store 업데이트 함수 (선택사항)
 * @returns 스타일 변경 성공 여부
 *
 * @example
 * ```typescript
 * const success = changeFeatureStyleAndSync(
 *   selectedFeature,
 *   { fill: { color: [255, 0, 0, 1] } },
 *   (featureId, newStyle) => {
 *     // 필요시 Store에 스타일 메타데이터 저장
 *     console.log(`Feature ${featureId} 스타일 변경됨:`, newStyle);
 *   }
 * );
 * ```
 */
export function changeFeatureStyle(
  feature: any,
  styleOptions: any,
  onStoreUpdate?: (featureId: string, newStyle: any) => void,
): boolean {
  if (!feature || !styleOptions) {
    console.warn(
      "changeFeatureStyleAndSync: feature 또는 styleOptions가 없습니다.",
    );
    return false;
  }

  try {
    // ✅ Core 메서드만 사용 (ODF 직접 접근 금지)
    const success = Feature.changeFeatureStyle(feature, styleOptions);

    if (success && onStoreUpdate) {
      // 피처 ID 추출 (안전한 방식)
      const featureId = feature.getId?.() || `feature_${Date.now()}`;
      onStoreUpdate(featureId, styleOptions);
    }

    return success;
  } catch (error) {
    console.error("changeFeatureStyleAndSync 실패:", error);
    return false;
  }
}

// ✅ deleteMeasureFeatureAndSync 제거됨 - 통합된 deleteFeature 사용

/**
 * 피처 ID 안전 추출 헬퍼
 *
 * @param feature - 피처 객체
 * @param prefix - ID 접두사 (기본값: "feature")
 * @returns 피처 ID 문자열
 */
export function extractFeatureId(
  feature: any,
  prefix: string = "feature",
): string {
  if (!feature) return `${prefix}_${Date.now()}`;

  // ODF 피처의 ID 추출 시도
  const id = feature.getId?.();
  if (id !== undefined && id !== null) {
    return String(id);
  }

  // ID가 없으면 타임스탬프 기반 생성
  return `${prefix}_${Date.now()}`;
}

/**
 * 스타일 옵션 검증 헬퍼
 *
 * @param styleOptions - 검증할 스타일 옵션
 * @returns 유효한 스타일 옵션 여부
 */
export function validateStyleOptions(styleOptions: any): boolean {
  if (!styleOptions || typeof styleOptions !== "object") {
    return false;
  }

  // 기본적인 스타일 속성 중 하나라도 있으면 유효
  const hasValidProperty =
    styleOptions.fill ||
    styleOptions.stroke ||
    styleOptions.image ||
    styleOptions.text;

  return !!hasValidProperty;
}

/**
 * 안전한 Feature → WKT 변환 유틸리티
 *
 * Circle geometry를 자동으로 Polygon으로 변환하여 WKT 생성.
 * 사용자가 매번 Circle → Polygon 변환을 신경쓸 필요 없음.
 *
 * @param feature - WKT로 변환할 피처 객체
 * @returns WKT 문자열 또는 null (실패시)
 *
 * @example
 * ```typescript
 * const wkt = featureToWKT(drawnFeature);
 * if (wkt) {
 *   console.log('WKT:', wkt);
 * }
 * ```
 */
export function featureToWKT(feature: any): string | null {
  if (!feature) {
    console.warn("featureToWKT: feature가 없습니다.");
    return null;
  }

  try {
    let processedFeature = feature;

    // Circle geometry인 경우 Polygon으로 변환
    const geometryType = feature.getGeometry()?.getType?.();
    if (geometryType && geometryType.includes("Circle")) {
      processedFeature = feature.toPolygon();
    }

    // 기존 featureToWKT 메서드 호출
    if (processedFeature.featureToWKT) {
      return processedFeature.featureToWKT();
    }

    // fallback: ODF FeatureFactory 사용
    if ((window as any).odf?.FeatureFactory?.toWKT) {
      const result = (window as any).odf.FeatureFactory.toWKT(processedFeature);
      return result?.wkt || null;
    }

    console.error("featureToWKT: WKT 변환 메서드를 찾을 수 없습니다.");
    return null;
  } catch (error) {
    console.error("featureToWKT 변환 실패:", error);
    return null;
  }
}
