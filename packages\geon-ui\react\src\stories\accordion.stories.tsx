import { Meta, StoryObj } from "@storybook/react-webpack5";

import { AccordionContent } from "../components/primitives/accordion";

const meta = {
  title: "radix-ui/AccordionContent",
  component: AccordionContent,
  parameters: {
    docs: {
      codePanel: true,
    },
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof AccordionContent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: `<Accordion
  className="w-[300px] rounded-md bg-mauve6 shadow-[0_2px_10px] shadow-black/5"
  type="single"
  defaultValue="item-1"
  collapsible
>
  <AccordionItem value="item-1">
    <AccordionTrigger>Is it accessible?</AccordionTrigger>
    <AccordionContent>
      Yes. It adheres to the WAI-ARIA design pattern.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-2">
    <AccordionTrigger>Is it unstyled?</AccordionTrigger>
    <AccordionContent>
      Yes. It&apos;s unstyled by default, giving you freedom over the look and
      feel.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-3">
    <AccordionTrigger>Can it be animated?</AccordionTrigger>
    <AccordionContent>
      Yes! You can animate the Accordion with CSS or JavaScript.
    </AccordionContent>
  </AccordionItem>
</Accordion>`,
      },
    },
  },
};
