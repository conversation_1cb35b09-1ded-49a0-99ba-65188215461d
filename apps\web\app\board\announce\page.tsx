"use client";

import { crtfckey } from "@geon-query/model";
import React from "react";

import SearchForm, { SearchOption } from "../_components/search-form";
import List from "./_components/tables/list";

export default function Announce() {
  const [params, setParams] = React.useState({
    buldDongNm: "",
    buldFloorNm: "",
    buldHoNm: "",
  });
  // 검색 옵션 정의
  const searchOptions: SearchOption[] = [
    { label: "동", value: "buldDongNm" },
    { label: "층", value: "buldFloorNm" },
    { label: "호", value: "buldHoNm" },
    { label: "소유자명", value: "ownerNm" },
  ];

  return (
    <>
      <SearchForm
        options={searchOptions}
        params={params}
        setParams={setParams}
        defaultField="ownerNm" // props로 기본값 지정 가능 (없으면 options[0])
      />
      <List
        pnu="4611010200116480010"
        crtfckey={crtfckey}
        numOfRows={10}
        pageNo={1}
        buldDongNm={params.buldDongNm}
        buldFloorNm={params.buldFloorNm}
        buldHoNm={params.buldHoNm}
      />
    </>
  );
}
