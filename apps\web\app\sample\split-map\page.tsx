"use client";

import { MapContainer } from "@geon-map/react-odf";
import { useState } from "react";

import BasemapWidgetUse from "@/components/widget/basemap-widget-use";

/**
 * 🎯 분할 모드 위젯 컴포넌트
 */
function SplitModeWidget() {
  const [splitCount, setSplitCount] = useState<1 | 2 | 3 | 4>(1);

  return (
    <>
      {/* 분할 모드 컨트롤 위젯 */}
      <div className="absolute right-4 top-4 z-50 rounded-lg bg-white/90 p-3 shadow-lg backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="flex rounded-md bg-gray-100 p-1">
            {[1, 2, 3, 4].map((count) => (
              <button
                key={count}
                onClick={() => setSplitCount(count as 1 | 2 | 3 | 4)}
                className={`rounded px-3 py-1 text-xs font-medium transition-colors ${
                  splitCount === count
                    ? "bg-blue-500 text-white shadow-sm"
                    : "text-gray-600 hover:bg-gray-200"
                }`}
              >
                {count}분할
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 지도 컨테이너 */}
      <MapContainer className="h-full w-full" splitMode={{ count: splitCount }}>
        <BasemapWidgetUse />
      </MapContainer>
    </>
  );
}

/**
 * 🎯 분할 지도 페이지
 */
export default function SplitMapPage() {
  return (
    <div className="relative h-screen w-full">
      <SplitModeWidget />
    </div>
  );
}
