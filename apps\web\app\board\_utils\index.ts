import {
  <PERSON><PERSON><PERSON>,
  Droplet,
  DropletOff,
  Fish,
  Route,
  ShieldCheck,
} from "lucide-react";

export type Service = {
  /** route 에 표시될 서비스 이름 (중복 불가) */
  id: string;
  title: string;
  icon: keyof typeof iconMap;
  items?: Omit<Service, "items" | "icon">[];
  [k: string]: any;
};

// 사용 아이콘
export const iconMap = {
  ChartArea,
  Droplet,
  DropletOff,
  Fish,
  Route,
  ShieldCheck,
} as const;

// 서비스 목록
export const SERVICES: Service[] = [
  {
    id: "announce",
    title: "공지사항",
    icon: "Route",
  },
  {
    id: "data",
    title: "데이터공간",
    icon: "Droplet",
  },
  {
    id: "qna",
    title: "질의응답",
    icon: "DropletOff",
  },
];

export const SERVICE_IDS = SERVICES.map((service) => service.id);

// Helper functions
export function getService(id: string): Service | undefined {
  return SERVICES.find((service) => service.id === id);
}

export function getSubService(id: string, subId: string) {
  return SERVICES.find((service) => service.id === id)?.items?.find(
    (sub) => sub.id === subId,
  );
}

export function isValidService(id: string): boolean {
  return SERVICE_IDS.includes(id);
}
