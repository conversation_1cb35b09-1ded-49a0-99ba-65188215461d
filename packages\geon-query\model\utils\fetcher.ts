import { Log } from "./Log";

export type RequestContentType =
  | "application/json"
  | "application/x-www-form-urlencoded";

export type ResponseContentType = "json" | "blob";

/** ### 응답 형식 처리를 위한 헬퍼 함수 (e.g. json, blob 등) */
const responseProcessors = {
  json: (response: Response) => response.json(),
  blob: async (response: Response) => {
    const blob = await response.blob();
    return { headers: response.headers, blob };
  },
} as const;

function encodeBody(
  body: Record<string, any>,
  contentType: RequestContentType,
): string {
  return contentType === "application/json"
    ? JSON.stringify(body)
    : new URLSearchParams(body as Record<string, string>).toString();
}

export const fetcher = {
  /** GET */
  get: async <T = unknown>(
    url: string,
    params?: Record<string, any>,
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    const queryString = params
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
          )
          .join("&")
      : "";

    const fullUrl = queryString
      ? url.includes("?")
        ? `${url}&${queryString}`
        : `${url}?${queryString}`
      : url;

    Log.logRequest("GET", fullUrl);

    try {
      const res = await fetch(fullUrl);
      const data = await responseProcessors[responseContentType](res);
      Log.logResponse("GET", fullUrl, data);
      return data as T;
    } catch (error) {
      Log.logError("GET", fullUrl, error);
      throw error;
    }
  },

  /** POST */
  post: async <
    T = unknown,
    B extends Record<string, any> = Record<string, any>,
  >(
    url: string,
    body: B,
    requestContentType: RequestContentType = "application/json",
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    Log.logRequest("POST", url, body);

    try {
      const headers: HeadersInit = { "Content-Type": requestContentType };
      const encodedBody = encodeBody(body, requestContentType);

      const res = await fetch(url, {
        method: "POST",
        headers,
        body: encodedBody,
      });

      const data = await responseProcessors[responseContentType](res);
      Log.logResponse("POST", url, data);
      return data as T;
    } catch (error) {
      Log.logError("POST", url, error);
      throw error;
    }
  },

  /** PUT */
  put: async <T = unknown, B extends Record<string, any> = Record<string, any>>(
    url: string,
    body: B,
    requestContentType: RequestContentType = "application/json",
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    Log.logRequest("PUT", url, body);

    try {
      const headers: HeadersInit = { "Content-Type": requestContentType };
      const encodedBody = encodeBody(body, requestContentType);

      const res = await fetch(url, {
        method: "PUT",
        headers,
        body: encodedBody,
      });

      const data = await responseProcessors[responseContentType](res);
      Log.logResponse("PUT", url, data);
      return data as T;
    } catch (error) {
      Log.logError("PUT", url, error);
      throw error;
    }
  },

  /** DELETE (바디는 선택적) */
  delete: async <
    T = unknown,
    B extends Record<string, any> | undefined = undefined,
  >(
    url: string,
    body?: B,
    requestContentType: RequestContentType = "application/json",
    responseContentType: ResponseContentType = "json",
  ): Promise<T> => {
    Log.logRequest("DELETE", url, body);

    try {
      const init: RequestInit = { method: "DELETE" };

      if (body && Object.keys(body as object).length > 0) {
        init.headers = { "Content-Type": requestContentType };
        init.body = encodeBody(body as Record<string, any>, requestContentType);
      }

      const res = await fetch(url, init);
      const data = await responseProcessors[responseContentType](res);
      Log.logResponse("DELETE", url, data);
      return data as T;
    } catch (error) {
      Log.logError("DELETE", url, error);
      throw error;
    }
  },
};
