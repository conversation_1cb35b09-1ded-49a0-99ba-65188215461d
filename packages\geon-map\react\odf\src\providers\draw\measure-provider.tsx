"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";
// 측정 옵션 타입은 core에서 직접 관리하므로 any로 수용
type MeasureControlOptions = any;

/**
 * MeasureProvider 설정 옵션
 */
export interface MeasureProviderOptions {
  /** Measure Control 초기화 옵션 */
  measureOptions?: MeasureControlOptions;
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 MeasureProvider (Measure Control 설정 전용)
 *
 * Measure Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <MeasureProvider measureOptions={{ tools: ["distance", "area"] }}>
 *       <MeasurementPanel />
 *     </MeasureProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function MeasureProvider({
  children,
  measureOptions = {
    tools: ["distance", "area", "round", "spot"],
    continuity: false,
    rightClickDelete: false,
  },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<MeasureProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Measure 설정 등록
    updateConfig({
      measureOptions,
      autoInitialize,
      onError,
    });
  }, [measureOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
