import { StateCreator, StoreMutatorIdentifier } from "zustand";

type Logger = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  <PERSON><PERSON> extends [StoreMutatorIdentifier, unknown][] = [],
>(
  f: StateCreator<T, Mps, Mcs>,
  name?: string,
) => StateCreator<T, Mps, Mcs>;

type LoggerImpl = <T>(
  f: StateCreator<T, [], []>,
  name?: string,
) => StateCreator<T, [], []>;

/**
 * Zustand 에서 발생한 state 변화를 기록하는 Logger 입니다.
 * @param f zustand create parameter
 * @param name 표시할 store 의 이름
 */
const loggerImpl: LoggerImpl = (f, name) => (set, get, store) => {
  const loggedSet: typeof set = (...a) => {
    set(...(a as Parameters<typeof set>));
    console.log(
      `🚀 ~ State Logger ~ set: %c${name ?? ""}%c`,
      "color:white; background:red; padding:2px 6px; border-radius:4px;",
      "",
      get(),
    );
  };

  const setState = store.setState;
  store.setState = (...a) => {
    setState(...(a as Parameters<typeof setState>));
    console.log(
      `🚀 ~ State Logger ~ store.setState: %c${name ?? ""}%c`,
      "color:white; background:red; padding:2px 6px; border-radius:4px;",
      "",
      store.getState(),
    );
  };

  return f(loggedSet, get, store);
};

export const logger = loggerImpl as unknown as Logger;
