"use client";

import { createEstateClient, crtfckey } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  Tabs,
  TabsContent,
  Tabs<PERSON>ist,
  TabsTrigger,
} from "@geon-ui/react/primitives/tabs";
import { useTranslations } from "next-intl";
import React from "react";

import LandBasic from "./tables/land-basic";
import LandUsePlan from "./tables/land-useplan";
import PriceInd from "./tables/price-ind";
import PricePclnd from "./tables/price-pclnd";
import RegistryHeadings from "./tables/registry-headings";

interface EstateDialogProps {
  pnu: string;
  tabValue: string;
  setTabValue: (value: string) => void;
  juso?: string;
}

export default function EstateDialogContent({
  pnu,
  tabValue,
  setTabValue,
  juso,
}: EstateDialogProps) {
  // message handler
  const t = useTranslations("dialog");
  const client = createEstateClient();

  return (
    <DialogContent className="max-h-[670px] w-full !max-w-[1000px]">
      <DialogHeader>
        <DialogTitle>통합 행정 정보 조회</DialogTitle>
        <DialogDescription>{juso || "Failed to get address"}</DialogDescription>
      </DialogHeader>
      <Tabs
        value={tabValue}
        className="h-[500px] w-full overflow-hidden overflow-y-auto"
      >
        <TabsList>
          <TabsTrigger
            value="land-basic"
            onClick={() => setTabValue("land-basic")}
          >
            토지 대장
          </TabsTrigger>
          <TabsTrigger
            value="registry-headings"
            onClick={() => setTabValue("registry-headings")}
          >
            건축물 대장
          </TabsTrigger>
          <TabsTrigger
            value="price-ind"
            onClick={() => setTabValue("price-ind")}
          >
            개별 주택 가격
          </TabsTrigger>
          <TabsTrigger
            value="price-pclnd"
            onClick={() => setTabValue("price-pclnd")}
          >
            공시지가
          </TabsTrigger>
          <TabsTrigger
            value="land-useplan"
            onClick={() => setTabValue("land-useplan")}
          >
            토지 이용 계획
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="land-basic"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <LandBasic pnu={pnu} client={client} pageNo={1} numOfRows={10} />
        </TabsContent>
        <TabsContent
          value="registry-headings"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <RegistryHeadings
            pnu={pnu}
            client={client}
            pageNo={1}
            numOfRows={10}
          />
        </TabsContent>
        <TabsContent
          value="price-ind"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <PriceInd pnu={pnu} pageNo={1} numOfRows={10} client={client} />
        </TabsContent>
        <TabsContent
          value="price-pclnd"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <PricePclnd pnu={pnu} pageNo={1} numOfRows={10} client={client} />
        </TabsContent>
        <TabsContent
          value="land-useplan"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <LandUsePlan
            pnu={pnu}
            crtfckey={crtfckey}
            pageNo={1}
            numOfRows={10}
            client={client}
          />
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="secondary">
            {t("close")}
          </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  );
}
