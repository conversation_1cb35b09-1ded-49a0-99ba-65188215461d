"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

export default function PricePclnd({
  client,
  ...props
}: APIRequestType<EstateClient["price"]["pclnd"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.price.pclnd");
  const f = useFormatter();
  // Pagination States
  const [numOfRows, setNumOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo, setPageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["price"]["pclnd"]>
  >({
    queryKey: ["price/pclnd", { ...props, numOfRows, pageNo }],
    queryFn: () => client.price.pclnd({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("stdrYear", {
      cell: (info) => info.getValue(),
      header: t("stdrYear"),
    }),
    helper.accessor("regstrSeCodeNm", {
      cell: (info) => info.getValue(),
      header: t("regstrSeCodeNm"),
    }),
    helper.accessor("pblntfDe", {
      cell: (info) => new Date(info.getValue()).toLocaleDateString(),
      header: t("pblntfDe"),
    }),
    helper.accessor("pblntfPclnd", {
      cell: (info) =>
        f.number(Number(info.getValue()), {
          style: "currency",
          currency: "KRW",
        }),
      header: t("pblntfPclnd"),
    }),
  ];

  return (
    <div className="flex w-full flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      {data.result.pageInfo && (
        <Pagination
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
