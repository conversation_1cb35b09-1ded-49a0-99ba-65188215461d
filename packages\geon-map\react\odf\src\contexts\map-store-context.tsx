"use client";

import type { ReactNode } from "react";
import React, { createContext, useContext, useMemo } from "react";

import { createDrawStore } from "../stores/draw-store";
import { createEventStore } from "../stores/event-store";
import { createLayerStore } from "../stores/layer-store";
import { createMapStore } from "../stores/map-store";

/**
 * 격리된 지도 스토어 초기 옵션 타입
 */
export interface MapStoreInitialOptions {
  map?: Partial<Parameters<typeof createMapStore>[0]>;
  event?: Partial<Parameters<typeof createEventStore>[0]>;
  layer?: Partial<Parameters<typeof createLayerStore>[0]>;
  draw?: Partial<Parameters<typeof createDrawStore>[0]>;
}

/**
 * 격리된 지도 스토어 컨텍스트 타입
 */
export interface MapStoreContextType {
  mapStore: ReturnType<typeof createMapStore>;
  eventStore: ReturnType<typeof createEventStore>;
  layerStore: ReturnType<typeof createLayerStore>;
  drawStore: ReturnType<typeof createDrawStore>;
  mapId: string;
}

// MapStore Context 생성
export const MapStoreContext = createContext<MapStoreContextType | null>(null);

/**
 * 지도 ID 생성 유틸리티
 */
const generateMapId = (): string => {
  return `map_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 격리된 지도 스토어 Provider
 *
 * 각 지도 컴포넌트에 대해 독립된 Zustand 스토어 인스턴스들을 생성하고 제공합니다.
 *
 * @example
 * ```tsx
 * // 독립적인 지도들
 * <MapStoreProvider mapId="map1" initialOptions={{...}}>
 *   <Map>
 *     <DrawProvider />
 *   </Map>
 * </MapStoreProvider>
 *
 * <MapStoreProvider mapId="map2" initialOptions={{...}}>
 *   <Map>
 *     <DrawProvider />
 *   </Map>
 * </MapStoreProvider>
 * ```
 */
export interface MapStoreProviderProps {
  children: ReactNode;
  mapId?: string;
  initialOptions?: MapStoreInitialOptions;
}

// 기본값 상수화로 참조 동등성 보장
const DEFAULT_INITIAL_OPTIONS: MapStoreInitialOptions = {};

export function MapStoreProvider({
  children,
  mapId = generateMapId(),
  initialOptions = DEFAULT_INITIAL_OPTIONS,
}: MapStoreProviderProps) {
  // 각 지도별로 독립된 스토어들을 생성
  const stores = useMemo(() => {
    return {
      mapStore: createMapStore(initialOptions.map),
      eventStore: createEventStore(initialOptions.event),
      layerStore: createLayerStore(initialOptions.layer),
      drawStore: createDrawStore(initialOptions.draw),
      mapId,
    };
  }, [mapId, initialOptions]);

  return (
    <MapStoreContext.Provider value={stores}>
      {children}
    </MapStoreContext.Provider>
  );
}

/**
 * 독립된 스토어를 가져오는 훅 (반드시 MapStoreProvider 내부에서 사용)
 *
 * 모든 Map은 독립된 스토어를 가져야 하므로 fallback 없이 강제합니다.
 */
export function useStores(): {
  mapStore: any;
  eventStore: any;
  layerStore: any;
  drawStore: any;
  mapId: string;
  isIsolated: boolean;
} {
  const context = useContext(MapStoreContext);

  if (!context) {
    throw new Error(
      "useStores는 MapStoreProvider 내부에서만 사용할 수 있습니다. " +
        "Map 컴포넌트는 반드시 독립된 스토어를 가져야 합니다.",
    );
  }

  return {
    mapStore: context.mapStore,
    eventStore: context.eventStore,
    layerStore: context.layerStore,
    drawStore: context.drawStore,
    mapId: context.mapId,
    isIsolated: true,
  };
}
