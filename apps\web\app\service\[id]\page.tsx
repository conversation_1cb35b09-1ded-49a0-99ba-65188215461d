import { MapContainer } from "@geon-map/react-odf";
import { notFound } from "next/navigation";

import { getService, SERVICES } from "../_utils";

export async function generateStaticParams() {
  return SERVICES.map((service) => ({
    id: service.id,
  }));
}

export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const service = getService(id);

  // 정의된 서비스가 아니면 404 return
  if (!service) {
    notFound();
  }

  // TODO: Auth Check

  return <MapContainer className="h-full w-full" zoom={5} />;
}
