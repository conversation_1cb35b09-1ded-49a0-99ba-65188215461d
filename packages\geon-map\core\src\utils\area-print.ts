import {
  AreaPrintOptions,
  PAPER_SIZES,
  PaperSize,
  PrintCanvasOptions,
  PrintImageOptions,
} from "../types/area-print";

export class AreaPrint {
  private options: AreaPrintOptions;

  constructor(options: AreaPrintOptions = {}) {
    this.options = {
      includeStyles: true,
      title: "　",
      paperSize: "A4", // 기본 용지 크기
      ...options,
    };
  }

  /**
   * 지정된 요소를 프린트합니다
   */
  print(element?: HTMLElement | string, paperSize?: PaperSize): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const targetElement = this.getTargetElement(
          element || this.options.element,
        );
        if (!targetElement) {
          throw new Error("프린트할 요소를 찾을 수 없습니다.");
        }

        // 새 창에서 프린트 실행
        this.openPrintWindow(targetElement, paperSize);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Canvas나 이미지 요소를 프린트합니다 (고품질 처리 제거)
   */

  async printCanvas(
    canvas: HTMLCanvasElement,
    options?: PrintCanvasOptions,
  ): Promise<void> {
    const { width, height, paperSize = "A4" } = options || {};

    // 1) toBlob 사용: 메모리/성능 이득 (Safari 대응 fallback 포함)
    const blob: Blob = await new Promise((resolve, reject) => {
      try {
        canvas.toBlob(
          (b) => (b ? resolve(b) : reject(new Error("toBlob failed"))),
          "image/png",
          0.92,
        );
      } catch (e) {
        // fallback: 마지막 수단으로만 사용
        try {
          const dataUrl = canvas.toDataURL("image/png", 0.92);
          const arr = dataUrl.split(",");

          // TypeScript 안전성 체크 추가 (arr[0]과 arr[1] 모두 체크)
          if (arr.length < 2 || !arr[0] || !arr[1]) {
            throw new Error("Invalid dataURL format");
          }

          const mime = arr[0].match(/:(.*?);/)?.[1] || "image/png";
          const bstr = atob(arr[1]); // 이제 arr[1]이 string임이 보장됨
          const n = bstr.length;
          const u8 = new Uint8Array(n);

          for (let i = 0; i < n; i++) {
            u8[i] = bstr.charCodeAt(i);
          }

          resolve(new Blob([u8], { type: mime }));
        } catch (err) {
          reject(err);
        }
      }
    });

    const url = URL.createObjectURL(blob);
    const img = document.createElement("img");
    img.src = url;
    img.decoding = "async";
    img.loading = "eager";

    // 2) 비율 유지: fill 금지
    if (width && height) {
      img.style.width = `${width}px`;
      img.style.height = `${height}px`;
      img.style.objectFit = "contain";
    } else {
      img.style.maxWidth = "100%";
      img.style.maxHeight = "100%";
      img.style.objectFit = "contain";
    }

    const container = document.createElement("div");
    // 3) 용지 마진 고려: 프린트 영역 안에서 contain
    container.style.display = "flex";
    container.style.alignItems = "center";
    container.style.justifyContent = "center";
    container.style.width = "100%";
    container.style.height = "100%";
    container.appendChild(img);

    // 4) 로드 대기 후 프린트
    await new Promise<void>((res, rej) => {
      img.onload = () => res();
      img.onerror = (e) => rej(e);
    });

    try {
      await this.print(container, paperSize);
    } finally {
      URL.revokeObjectURL(url);
    }
  }
  /**
   * 미리보기용 캔버스를 생성합니다 (간단한 버전)
   */
  createPreviewCanvas(
    canvas: HTMLCanvasElement,
    paperSize: PaperSize = "A4",
    maxPreviewSize: number = 400, // 기본 크기를 줄임
  ): HTMLCanvasElement {
    const previewCanvas = document.createElement("canvas");
    const ctx = previewCanvas.getContext("2d");

    if (!ctx) {
      throw new Error("Canvas 2D context를 생성할 수 없습니다.");
    }

    // 간단한 비율 계산
    const scale = Math.min(
      maxPreviewSize / canvas.width,
      maxPreviewSize / canvas.height,
    );

    const previewWidth = Math.round(canvas.width * scale);
    const previewHeight = Math.round(canvas.height * scale);

    // 캔버스 크기 설정
    previewCanvas.width = previewWidth;
    previewCanvas.height = previewHeight;

    // 기본 품질 설정 (고품질 옵션 제거)
    ctx.imageSmoothingEnabled = true;

    // 이미지 그리기
    ctx.drawImage(
      canvas,
      0,
      0,
      canvas.width,
      canvas.height,
      0,
      0,
      previewWidth,
      previewHeight,
    );

    return previewCanvas;
  }

  /**
   * HTML을 이미지로 변환하여 프린트합니다 (고품질 옵션 제거)
   */
  async printAsImage(
    element: HTMLElement | string,
    options?: PrintImageOptions,
  ): Promise<void> {
    try {
      const { paperSize = "A4", ...imageOptions } = options || {};

      // html-to-image 동적 임포트
      const htmlToImage = await import("html-to-image");

      const targetElement = this.getTargetElement(element);
      if (!targetElement) {
        throw new Error("프린트할 요소를 찾을 수 없습니다.");
      }

      // 기본 품질로 이미지 변환 (고품질 옵션 제거)
      const dataUrl = await htmlToImage.toPng(targetElement, {
        width: imageOptions.width,
        height: imageOptions.height,
        backgroundColor: imageOptions.backgroundColor || "#ffffff",
        pixelRatio: 1, // 픽셀 비율을 1로 고정하여 속도 향상
        quality: 0.8, // 품질을 낮춰서 속도 향상
      });

      // 임시 이미지 요소 생성하여 프린트
      const img = document.createElement("img");
      img.src = dataUrl;
      img.style.maxWidth = "100%";
      img.style.height = "auto";

      const container = document.createElement("div");
      container.appendChild(img);

      return this.print(container, paperSize);
    } catch (error) {
      console.error("이미지 변환 중 오류:", error);
      throw error;
    }
  }

  private getTargetElement(element?: HTMLElement | string): HTMLElement | null {
    if (!element) return null;

    if (typeof element === "string") {
      return document.querySelector(element);
    }

    return element;
  }

  private openPrintWindow(element: HTMLElement, paperSize?: PaperSize): void {
    const printWindow = window.open(
      "",
      "_blank",
      "width=800,height=600,scrollbars=yes,resizable=yes,location=no,menubar=no,toolbar=no,status=no",
    );

    if (!printWindow) {
      throw new Error("팝업 차단으로 인해 프린트 창을 열 수 없습니다.");
    }

    const selectedPaperSize = paperSize || this.options.paperSize || "A4";
    const paperDimensions = PAPER_SIZES[selectedPaperSize];

    const styles = this.options.includeStyles ? this.getAllStyles() : "";
    const customStyles = this.options.customStyles || "";

    // 새 창의 document 참조
    const doc = printWindow.document;

    // HTML 전체 구조를 한 번에 설정
    doc.documentElement.innerHTML = `
    <head>
      <meta charset="utf-8">
      <title>${this.options.title}</title>
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: Arial, sans-serif;
        }
        
        @media print {
          @page {
            size: ${paperDimensions.width}mm ${paperDimensions.height}mm;
            margin: 10mm;
          }  
          body {
            margin: 0;
            padding: 0;
            width: 100vw;
            height: 100vh;
          }
          
          .print-content {
            width: 100vw;
            height: 100vh;  /* auto → 100vh */
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .print-content img {
            width: 100vw !important;
            height: 100vh !important;
            object-fit: cover;  /* contain → cover */
          }
        }
        ${customStyles}
      </style>
      ${styles}
    </head>
    <body>
      <div class="print-content">
        ${element.outerHTML}
      </div>
    </body>
  `;

    // 스크립트는 보안상 별도로 생성하여 추가
    const script = doc.createElement("script");
    script.textContent = `
    window.onload = function() {
      setTimeout(function() {
        window.print();
        window.onafterprint = function() {
          window.close();
        };
      }, 300); // 로딩 시간 단축
    };
  `;
    doc.head.appendChild(script);
  }

  private getAllStyles(): string {
    const styleSheets = Array.from(document.styleSheets);
    let allStyles = "";

    styleSheets.forEach((styleSheet) => {
      try {
        if (styleSheet.cssRules) {
          const rules = Array.from(styleSheet.cssRules);
          rules.forEach((rule) => {
            allStyles += rule.cssText + "\n";
          });
        }
      } catch (e) {
        // CORS 정책으로 인해 접근할 수 없는 스타일시트는 무시
        console.warn("스타일시트에 접근할 수 없습니다:", e);
      }
    });

    return `<style>${allStyles}</style>`;
  }

  /**
   * 브라우저의 기본 프린트 대화상자를 엽니다
   */
  static printPage(): void {
    window.print();
  }

  /**
   * 특정 영역만 프린트하기 위한 CSS 미디어 쿼리를 적용합니다
   */
  static setPrintOnly(selector: string): void {
    const style = document.createElement("style");
    style.textContent = `
      @media print {
        body * {
          visibility: hidden;
        }
        ${selector}, ${selector} * {
          visibility: visible;
        }
        ${selector} {
          position: absolute;
          left: 0;
          top: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }
}
