import { NextRequest, NextResponse } from "next/server";

// Mock 공통 코드 데이터
const COMMON_CODES: Record<
  string,
  Array<{ code: string; name: string; order?: number }>
> = {
  JMP001: [
    { code: "01", name: "고속도로", order: 1 },
    { code: "02", name: "국도", order: 2 },
    { code: "03", name: "지방도", order: 3 },
    { code: "04", name: "시군도", order: 4 },
    { code: "05", name: "기타도로", order: 5 },
  ],

  FACILITY_TYPE: [
    { code: "BRIDGE", name: "교량", order: 1 },
    { code: "TUNNEL", name: "터널", order: 2 },
    { code: "SAFETY", name: "안전시설", order: 3 },
    { code: "TRAFFIC", name: "교통시설", order: 4 },
    { code: "DRAINAGE", name: "배수시설", order: 5 },
  ],

  FACILITY_STATUS: [
    { code: "NORMAL", name: "정상", order: 1 },
    { code: "REPA<PERSON>", name: "수리중", order: 2 },
    { code: "DAMAGED", name: "손상", order: 3 },
    { code: "CLOSED", name: "폐쇄", order: 4 },
  ],

  PARK_TYPE: [
    { code: "URBAN", name: "도시공원", order: 1 },
    { code: "NEIGHBORHOOD", name: "근린공원", order: 2 },
    { code: "CHILDREN", name: "어린이공원", order: 3 },
    { code: "SPORTS", name: "체육공원", order: 4 },
    { code: "THEME", name: "주제공원", order: 5 },
  ],

  ADMIN_DISTRICT: [
    { code: "MUAN", name: "무안읍", order: 1 },
    { code: "SAMHYANG", name: "삼향읍", order: 2 },
    { code: "MONGTAN", name: "몽탄면", order: 3 },
    { code: "CHEONGYE", name: "청계면", order: 4 },
    { code: "HYEONGYEONG", name: "현경면", order: 5 },
    { code: "MANIN", name: "만인면", order: 6 },
    { code: "UNMUN", name: "운문면", order: 7 },
    { code: "CHEONGIL", name: "청일면", order: 8 },
    { code: "HAENAM", name: "해남면", order: 9 },
    { code: "ILLO", name: "일로읍", order: 10 },
    { code: "DOCSAN", name: "도산면", order: 11 },
  ],
};

export async function GET(
  request: NextRequest,
  { params }: { params: { codeGroup: string } },
) {
  try {
    const { codeGroup } = await params;

    // Mock 데이터에서 코드 그룹 조회
    const codes = COMMON_CODES[codeGroup];

    if (!codes) {
      return NextResponse.json(
        { error: `Code group not found: ${codeGroup}` },
        { status: 404 },
      );
    }

    // order 기준으로 정렬 후 API 응답 형태로 변환
    const sortedCodes = codes
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map((code) => ({
        value: code.code,
        label: code.name,
      }));

    return NextResponse.json(sortedCodes);
  } catch (error) {
    console.error("Error fetching common codes:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
