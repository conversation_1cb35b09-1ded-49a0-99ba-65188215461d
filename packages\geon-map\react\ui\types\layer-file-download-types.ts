// 레이어 파일 다운로드 포맷 유형 타입
export type LayerFileDownloadFormat =
  | "csv"
  | "shape"
  | "kml"
  | "geojson"
  | "kmz"
  | "dxf";

// 레이어 파일 다운로드 콜백 함수 타입
export type LayerFileDownloadCallback<TParams, TResult> = (
  layerFileDownloadInfo: TParams,
) => Promise<TResult | undefined>;

// 전체 Context Value 인터페이스 타입
export interface LayerFileDownloadContextValue<TParams, TResult> {
  layerFileDownloadInfo?: TParams;
  handleLayerFileDownloadInMemory?: LayerFileDownloadCallback<TParams, TResult>;
  isLoading?: boolean;
}

// 컴포넌트 Props 인테페이스들
type ButtonName = {
  buttonName: string | "";
};
type LayerFileDownloadFormatList = {
  activeDownloadFormats: LayerFileDownloadFormat[];
};
export interface LayerFileDownloadWidgetProps<TParams, TResult>
  extends LayerFileDownloadContextValue<TParams, TResult>,
    React.ComponentPropsWithoutRef<"div">,
    LayerFileDownloadFormatList,
    ButtonName {}
export interface LayerFileDownloadProps<TParams, TResult>
  extends React.ComponentPropsWithoutRef<"div">,
    LayerFileDownloadContextValue<TParams, TResult> {}
export interface LayerFileDownloadTriggerProps
  extends React.ComponentPropsWithoutRef<"button">,
    ButtonName {}
export interface LayerFileDownloadContentProps
  extends React.ComponentPropsWithoutRef<"div"> {}
export interface LayerFileDownloadItemProps<TParams, TResult>
  extends React.ComponentPropsWithoutRef<"button">,
    LayerFileDownloadContextValue<TParams, TResult> {
  layerFileDownloadType: LayerFileDownloadFormat;
}
