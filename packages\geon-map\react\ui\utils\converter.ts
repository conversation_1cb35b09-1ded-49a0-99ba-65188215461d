import { AddrgeoClient, APIResponseType, JusoItem } from "@geon-query/model";
import { groupBy } from "es-toolkit/array";

import { BasemapMapType } from "../components";
import { AddressBase, AddressSearchTypes } from "../types";
import {
  RegionData,
  RegionInfo,
  RegionItem,
} from "../types/region-selector-types";

// APIResponseType를 이용한 타입선언
type AdministResponse = APIResponseType<AddrgeoClient["administ"]["ctpv"]>;
type AddressSearchResponse = APIResponseType<AddrgeoClient["address"]["bld"]>;
type MapItem = {
  bcrnMapNm: string;
  bcrnMapClCodeNm: string;
  lyrStleCodeNm: string;
  mapUrl: string;
  base64: string;
  mapUrlparamtr: string;
};
export type BaseMapTypeGroup = Record<string, MapItem[]>;

export const basemapWidget = (result: BasemapMapType): BaseMapTypeGroup => {
  try {
    const normalized: MapItem[] = result.map(
      (item: BasemapMapType[number]) => ({
        bcrnMapNm: item.bcrnMapNm,
        bcrnMapClCodeNm: String(item.bcrnMapClCodeNm ?? "").trim(),
        lyrStleCodeNm: item.lyrStleCodeNm,
        mapUrl: item.mapUrl,
        mapUrlparamtr: item.mapUrlparamtr,
        base64: (item as any).base64, // 있으면 받고, 없으면 undefined
      }),
    );

    // 그룹화 (키가 없으면 안전하게 "": 빈 문자열 그룹으로 들어감)
    const grouped = groupBy(normalized, (it) => it.bcrnMapClCodeNm || "");

    // 필요하면 빈 키("")는 제거하거나 이름 바꿔도 됨
    // if (grouped[""]) delete grouped[""];
    return grouped;
  } catch (e) {
    console.error("[basemapWidget] groupBy failed:", e);
    return {};
  }
};

export const addressSearchWidget = ({
  result,
  searchType,
  srid,
}: {
  result: AddressSearchResponse;
  searchType: AddressSearchTypes;
  srid: string;
}): {
  resultList: AddressBase[];
  hasNextPage: boolean;
} => {
  try {
    if (result.code === 200) {
      const resultList = result.result.jusoList.map((item: JusoItem) => {
        const convertedItem: AddressBase = {
          srid: srid.toString(),
          roadAddress: item.roadAddr,
          jibunAddress: item.jibunAddr,
          buildingName: item.buildName,
          poiName: item.poiName,
        };

        /**
         * [결과 geometry 타입]
         * - Polygon(폴리곤) 표출:
         *    integrated, jibun, roadApi, road, building, postalCode, pnu
         * - Point(포인트) 표출:
         *    coordinates, poi
         */

        /**
         * [geometry 설정 우선순위]
         * - 통합/지번/도로명주소/건물명/기초구역번호(예:03070):
         *     1. 건물 폴리곤이 있으면 우선 사용
         *     2. 없으면 필지(토지) 폴리곤 사용
         * - pnu:
         *     필지(토지) 폴리곤만 표출
         * - poi:
         *     poi의 x, y 좌표만 표출
         * - 경위도 좌표:
         *     1. 건물 x, y가 있으면 우선 사용
         *     2. 없으면 필지 x, y 사용
         */

        // 검색 타입에 따른 geometry 설정
        if (searchType === "coordinates") {
          const useBuild = item.buildX && item.buildY;
          convertedItem.x = parseFloat(
            useBuild ? (item.buildX ?? "") : (item.parcelX ?? ""),
          );
          convertedItem.y = parseFloat(
            useBuild ? (item.buildY ?? "") : (item.parcelY ?? ""),
          );
        } else if (searchType === "poi") {
          convertedItem.x = parseFloat(item.poiX ?? "");
          convertedItem.y = parseFloat(item.poiY ?? "");
        } else if (searchType === "pnu") {
          // PNU는 필지 폴리곤만 표출
          convertedItem.geometryWKT = item.geom;
        } else {
          // 나머지는 건물 폴리곤 우선, 없으면 필지 폴리곤
          convertedItem.geometryWKT = item.buildGeom || item.geom;
        }
        return convertedItem;
      });
      return {
        resultList,
        hasNextPage:
          Number(result.result.common.totalCount) >
          Number(
            result.result.common.currentPage *
              result.result.common.countPerPage,
          ),
      };
    }
    return {
      resultList: [],
      hasNextPage: false,
    };
  } catch (e) {
    console.error(e);
    return {
      resultList: [],
      hasNextPage: false,
    };
  }
};
export const RegionSelectorWidget = {
  item: (result: AdministResponse): RegionItem | null => {
    try {
      if (result.code === 200) {
        const data = result.result[0];
        if (data) {
          return {
            code: data.cd,
            name: data.korNm,
            wktPolygon: data.geom,
          };
        }
      }
    } catch (e) {
      console.error(e);
      return null;
    }
    return null;
  },
  list: (result: AdministResponse): RegionInfo[] | null => {
    try {
      if (result.code === 200) {
        const data = result.result;
        if (data) {
          return data.map((item) => {
            return {
              code: item.cd,
              name: item.korNm,
            };
          });
        }
      }
    } catch (e) {
      console.error(e);
      return null;
    }
    return null;
  },
  regionCodesFromCoord: (result: AddressSearchResponse): RegionData | null => {
    try {
      if (result.code === 200) {
        const data = result.result;
        if (data?.jusoList[0]?.addrPnu) {
          const pnu = data.jusoList[0].addrPnu;
          return {
            sido: pnu.substring(0, 2),
            sigungu: pnu.substring(0, 5),
            eupmyeondong: pnu.substring(0, 8),
            li: pnu.substring(0, 10),
          };
        }
      }
      return null;
    } catch (e) {
      console.dir(e);
      return null;
    }
  },
};
