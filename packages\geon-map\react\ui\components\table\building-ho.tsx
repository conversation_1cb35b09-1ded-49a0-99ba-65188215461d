"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function BuildingHoTable({
  client,
  ...props
}: APIRequestType<EstateClient["building"]["ho"]> & {
  client: EstateClient;
}) {
  // Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["building"]["ho"]>
  >({
    queryKey: ["building/ho", { ...props, numOfRows, pageNo }],
    queryFn: () => client.building.ho({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError)
    return (
      <div className="flex justify-center align-middle">
        Error loading parcel data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">
              대지권 일련번호
            </TableHead>
            <TableHead className="font-bold text-center">대장 구분</TableHead>
            <TableHead className="font-bold text-center">건물</TableHead>
            <TableHead className="font-bold text-center">동</TableHead>
            <TableHead className="font-bold text-center">층</TableHead>
            <TableHead className="font-bold text-center">호실</TableHead>
            <TableHead className="font-bold text-center">대지권비율</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`building-floor-${idx}`}>
                <TableCell className="text-center">{res.agbldgSn}</TableCell>
                <TableCell className="text-center">
                  {res.regstrSeCodeNm}
                </TableCell>
                <TableCell className="text-center">{res.buldNm}</TableCell>
                <TableCell className="text-center">{res.buldDongNm}</TableCell>
                <TableCell className="text-center">{res.buldFloorNm}</TableCell>
                <TableCell className="text-center">{res.buldHoNm}</TableCell>
                <TableCell className="text-center">{res.ldaQotaRate}</TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                No data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
