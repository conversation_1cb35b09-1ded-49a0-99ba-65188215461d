"use-client";

import { MoveMapHomeWidget } from "@geon-map/react-ui/components";

interface MoveMapHomeWidgetPackageProps {
  center: [number, number];
  zoom: number;
  duration?: number;
  className?: string;
}

export default function MoveMapHomeWidgetPackage({
  zoom,
  center,
  duration = 500,
  className = "absolute right-4 top-50 flex flex-col gap-2",
}: MoveMapHomeWidgetPackageProps) {
  return (
    <MoveMapHomeWidget
      zoom={zoom}
      center={center}
      duration={duration}
      className={className}
    />
  );
}
