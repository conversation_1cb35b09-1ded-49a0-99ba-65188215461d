import { Overview, OverviewMapPositionType } from "@geon-map/core";
import { useEffect, useRef, useState } from "react";

import { useMap } from "./use-map";

export function useOverview(position: OverviewMapPositionType = "left-down") {
  const [enabled, setEnabled] = useState(false);
  const isInitializedRef = useRef(false);
  const { map, odf, isLoading, error } = useMap();

  if (isLoading || error || !map || !odf) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }
  const overviewInstanceRef = useRef<Overview | null>(null);

  // Overview 인스턴스 생성
  const getOverviewInstance = () => {
    if (!overviewInstanceRef.current) {
      overviewInstanceRef.current = new Overview(map, odf);
    }
    return overviewInstanceRef.current;
  };

  // 초기 설정
  useEffect(() => {
    if (isInitializedRef.current) return;
    const instance = getOverviewInstance();
    if (!instance) return;

    instance.setMap();
    instance.changeOverviewMapPosition(position);

    if (instance.getState() !== enabled) {
      instance.changeState(); // 초기 상태 꺼짐으로 맞춤
    }

    isInitializedRef.current = true;
  }, [map, odf, position, enabled]);

  // 토글 시 changeState 호출
  useEffect(() => {
    if (!isInitializedRef.current) return;
    const instance = getOverviewInstance();
    if (instance) {
      instance.changeState();
    }
  }, [enabled]);

  return { enabled, setEnabled };
}
