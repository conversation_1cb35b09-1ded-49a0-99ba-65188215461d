"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { HTMLAttributes } from "react";

/** 한 페이지에 표시할 행 개수 선택 옵션 */
const NUM_OF_ROWS = [10, 20, 25, 30, 40, 50];

export interface PaginationProps extends HTMLAttributes<HTMLDivElement> {
  /** pagination 정보 */
  pageInfo: {
    numOfRows: number;
    pageNo: number;
    totalCount: string;
  };
  onPageNoChange: (pageNo: number) => void;
  onNumOfRowsChange: (numOfRows: number) => void;
  isLoading?: boolean;
}

/**
 * Server 에서 처리된 Pagination 에 따라 `numOfRows` 와 `pageNo` 요청을 처리합니다.
 */
export default function Pagination({
  pageInfo,
  onPageNoChange,
  onNumOfRowsChange,
  isLoading = false,
  className,
}: PaginationProps) {
  // message handler
  const t = useTranslations("pagination");

  const totalCount = parseInt(pageInfo.totalCount);
  const totalPages = Math.ceil(totalCount / pageInfo.numOfRows);
  const currentPage = pageInfo.pageNo;
  const canPreviousPage = currentPage > 1;
  const canNextPage = currentPage < totalPages;

  return (
    <div className={cn("flex items-center justify-between px-2", className)}>
      {/* Showing status */}
      <div className="text-muted-foreground flex-1 text-sm">
        {t("resultStatus", {
          totalCount,
          start: (currentPage - 1) * pageInfo.numOfRows + 1,
          end: Math.min(currentPage * pageInfo.numOfRows, totalCount),
        })}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        {/* Rows per page selector */}
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">{t("rowsPerPage")}</p>
          <Select
            value={`${pageInfo.numOfRows}`}
            onValueChange={(value) => {
              onNumOfRowsChange(Number(value));
            }}
            disabled={isLoading}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={pageInfo.numOfRows} />
            </SelectTrigger>
            <SelectContent side="top">
              {NUM_OF_ROWS.map((numOfRows) => (
                <SelectItem key={numOfRows} value={`${numOfRows}`}>
                  {numOfRows}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {/* Page indicator */}
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          {t("pageStatus", { currentPage, totalPages })}
        </div>
        {/* Navigation buttons */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="hidden size-8 lg:flex"
            title="처음"
            onClick={() => onPageNoChange(1)}
            disabled={!canPreviousPage || isLoading}
          >
            {/* << */}
            <ChevronsLeft />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="size-8"
            title="이전"
            onClick={() => onPageNoChange(currentPage - 1)}
            disabled={!canPreviousPage || isLoading}
          >
            {/* < */}
            <ChevronLeft />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="size-8"
            title="다음"
            onClick={() => onPageNoChange(currentPage + 1)}
            disabled={!canNextPage || isLoading}
          >
            {/* > */}
            <ChevronRight />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="hidden size-8 lg:flex"
            title="마지막"
            onClick={() => onPageNoChange(totalPages)}
            disabled={!canNextPage || isLoading}
          >
            {/* >> */}
            <ChevronsRight />
          </Button>
        </div>
      </div>
    </div>
  );
}
