"use client";

import { DndContext, useDraggable } from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Button } from "@geon-ui/react/primitives/button";
import { X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

/**
 * 드래그 가능한 모달 콘텐츠 컴포넌트
 */
function DraggableModalContent({
  children,
  onClose,
  title,
  position,
  showCloseButton = true,
}: {
  children: React.ReactNode;
  onClose: () => void;
  title: React.ReactNode;
  position: { x: number; y: number };
  showCloseButton?: boolean;
}) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: "draggable-modal",
  });

  // transform과 position을 합쳐서 최종 위치 계산
  const style = {
    transform: transform
      ? `translate3d(${transform.x + position.x}px, ${transform.y + position.y}px, 0)`
      : `translate3d(${position.x}px, ${position.y}px, 0)`,
    left: "50%",
    top: "50%",
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="fixed z-50 -translate-x-1/2 -translate-y-1/2"
    >
      <div className="max-h-[90vh] w-full max-w-4xl rounded-lg border bg-white shadow-lg">
        {/* 드래그 핸들 헤더 */}
        <div className="flex items-center justify-between rounded-t-lg border-b bg-gray-50 p-4">
          <div
            {...listeners}
            {...attributes}
            className="flex-1 cursor-grab select-none active:cursor-grabbing"
          >
            {title}
          </div>
          {showCloseButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="ml-2 h-auto p-1 hover:bg-gray-200"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 모달 콘텐츠 */}
        <div className="max-h-[calc(90vh-80px)] overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * 드래그 가능한 모달 컴포넌트 Props
 */
export interface DraggableModalProps {
  /** 모달 열림/닫힘 상태 */
  isOpen: boolean;
  /** 모달 닫기 함수 */
  onClose: () => void;
  /** 모달 제목 */
  title: React.ReactNode;
  /** 모달 콘텐츠 */
  children: React.ReactNode;
  /** 닫기 버튼 표시 여부 */
  showCloseButton?: boolean;
  /** 추가 CSS 클래스 */
  className?: string;
}

/**
 * 재사용 가능한 드래그 가능한 모달 컴포넌트
 */
export function DraggableModal({
  isOpen,
  onClose,
  title,
  children,
  showCloseButton = true,
  className,
}: DraggableModalProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // 모달이 닫힐 때 위치 초기화
  useEffect(() => {
    if (!isOpen) {
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.stopPropagation();
        onClose();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  // 드래그 종료 시 위치 업데이트
  const handleDragEnd = useCallback((event: any) => {
    if (event.delta) {
      setPosition((prev) => ({
        x: prev.x + event.delta.x,
        y: prev.y + event.delta.y,
      }));
    }
  }, []);

  if (!isOpen) return null;

  return (
    <DndContext onDragEnd={handleDragEnd} modifiers={[restrictToWindowEdges]}>
      <DraggableModalContent
        onClose={onClose}
        title={title}
        position={position}
        showCloseButton={showCloseButton}
      >
        <div className={className}>{children}</div>
      </DraggableModalContent>
    </DndContext>
  );
}

export default DraggableModal;
