"use client";

import { cn } from "@geon-ui/react/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "./sidebar/home-button";

export default function AdminSidebar({
  className,
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { state, open, setOpen, toggleSidebar } = useSidebar();

  return (
    <Sidebar variant="sidebar" className={cn("z-10", className)} {...props}>
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Sidebar Group</SidebarGroupLabel>
          <SidebarMenuButton><PERSON><PERSON></SidebarMenuButton>
          <SidebarMenuSub>
            <SidebarMenuSubItem>
              <SidebarMenuSubButton>Sub Menu 1</SidebarMenuSubButton>
            </SidebarMenuSubItem>
            <SidebarMenuSubItem>
              <SidebarMenuSubButton>Sub Menu 2</SidebarMenuSubButton>
            </SidebarMenuSubItem>
          </SidebarMenuSub>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>Footer</SidebarFooter>
    </Sidebar>
  );
}
