import { O<PERSON>, ODF_MAP, ODF_PRINT_CONTROL, PrintControlOptions } from "../types";

export class Print {
  #printControl: ODF_PRINT_CONTROL;
  constructor(odf: ODF, options?: PrintControlOptions) {
    this.#printControl = new odf.printControl(options || {});
  }

  /**
   * printControl과 map 연결
   */
  setMap(map: ODF_MAP, createElementFlag: boolean = false): void {
    this.#printControl.setMap(map, createElementFlag);
  }

  /**
   * printControl과 map 연결 해제
   */
  removeMap(): void {
    this.#printControl.removeMap();
  }

  /**
   * 프틴트 실행
   */
  print(): void {
    this.#printControl.prints();
  }

  /**
   * 컨트롤 생성 옵션 반환
   */
  getConstructorOptions(): Array<object> {
    return this.#printControl.getConstructorOptions();
  }

  /**
   * printControl 인스턴스에 직접 접근
   */
  get printControl() {
    return this.#printControl;
  }
}
