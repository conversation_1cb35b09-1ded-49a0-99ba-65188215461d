"use-client";
import type { APIRequestType, APIResponseType } from "@geon-query/model";
import type { GeonAnalysisClient } from "@geon-query/model/restapi/analysis";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import {
  Download as DownloadIcon,
  DraftingCompass,
  FileArchive,
  FileCode2,
  FileJson2,
  FileSpreadsheet,
  Shapes,
} from "lucide-react";
import React from "react";
import { useMemo } from "react";

import {
  LayerFileDownloadContext,
  useLayerFileDownloadContext,
} from "../../contexts";
import type {
  LayerFileDownloadCallback,
  LayerFileDownloadContentProps,
  LayerFileDownloadContextValue,
  LayerFileDownloadFormat,
  LayerFileDownloadItemProps,
  LayerFileDownloadProps,
  LayerFileDownloadTriggerProps,
  LayerFileDownloadWidgetProps,
} from "../../types/layer-file-download-types";
import { downloadBlobFile, getFilenameFromHeader } from "../../utils";

/** 활성화 할 레이어 파일 다운로드 버튼에 대한 포맷별 옵션 */
const LAYER_FILE_DOWNLOAD_FORMATS = {
  csv: {
    label: "CSV",
    IconComponent: FileSpreadsheet,
    className: "text-green-500",
  },
  shape: {
    label: "SHP",
    IconComponent: Shapes,
    className: "text-sky-600",
  },
  geojson: {
    label: "GEOJSON",
    IconComponent: FileJson2,
    className: "text-teal-500",
  },
  kml: {
    label: "KML",
    IconComponent: FileCode2,
    className: "text-red-600",
  },
  kmz: {
    label: "KMZ",
    IconComponent: FileArchive,
    className: "text-yellow-600",
  },
  dxf: {
    label: "DXF",
    IconComponent: DraftingCompass,
    className: "text-purple-500",
  },
} as const;

// API Request Param, Response Result 명시용 타입(geon-query 외부용)
type LayerFileDownloadParam = APIRequestType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;
type LayerFileDownloadResult = APIResponseType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;

/**
 * ### 레이어 파일 다운로드 헬퍼함수
 * ---
 * @param layerFileDownloadType 레이어 파일 다운로드 타입 (e.g. "csv", "shape", "kml", "geojson", "kmz", "dxf")
 * @param param Context 내 LayerFileDownloadRequest
 * @param handleLayerFileDownloadInMemory Context 내 LayerFileDownloadCallback 함수(use-layer-file-download.ts 의 handleLayerFileDownloadInMemory)
 * @return 레이어 파일(e.g. '.csv', '.zip', '.geojson', '.kml', '.kmz', '.dxf')
 */
const downloadLayerFile = async (
  layerFileDownloadType: LayerFileDownloadFormat,
  params: LayerFileDownloadParam,
  handleLayerFileDownloadInMemory:
    | LayerFileDownloadCallback<LayerFileDownloadParam, LayerFileDownloadResult>
    | undefined,
) => {
  try {
    params.outputFormat = layerFileDownloadType;
    const result = await handleLayerFileDownloadInMemory?.(params);
    if (result) {
      const { headers, blob } = result;
      const filename =
        getFilenameFromHeader(headers) ?? "downloaded-file.geojson";
      downloadBlobFile(filename, blob);
    }
  } catch (error) {
    console.error("Layer file download failed: ", error);
  }
};

// 컴포넌트들
export const LayerFileDownloadTrigger = React.forwardRef<
  HTMLButtonElement,
  LayerFileDownloadTriggerProps
>(({ buttonName, className, children, ...props }, ref) => {
  return (
    <HoverCardTrigger asChild>
      <Button
        ref={ref}
        className={cn(
          "cursor-pointer bg-white text-black hover:bg-black hover:text-white opacity-80 border h-10",
          className,
        )}
        {...props}
      >
        {buttonName}
        {children}
      </Button>
    </HoverCardTrigger>
  );
});
LayerFileDownloadTrigger.displayName = "LayerFileDownloadTrigger";

export const LayerFileDownloadContent = React.forwardRef<
  React.ComponentRef<typeof HoverCardContent>,
  LayerFileDownloadContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <HoverCardContent
      ref={ref}
      className={cn(
        "flex flex-row p-1 gap-3 bg-background/90 backdrop-blur-md border shadow-lg rounded-md w-fit h-auto items-center justify-center",
        className,
      )}
      align="center"
      side="bottom"
      alignOffset={-40}
      sideOffset={1}
      {...props}
    >
      {children}
    </HoverCardContent>
  );
});
LayerFileDownloadContent.displayName = "LayerFileDownloadContent";

export const LayerFileDownloadItem = React.forwardRef<
  HTMLButtonElement,
  LayerFileDownloadItemProps<LayerFileDownloadParam, LayerFileDownloadResult>
>(({ className, children, layerFileDownloadType, ...props }, ref) => {
  const { layerFileDownloadInfo, handleLayerFileDownloadInMemory } =
    useLayerFileDownloadContext();

  const onClickDownloadLayerFile = () => {
    if (layerFileDownloadInfo) {
      downloadLayerFile(
        layerFileDownloadType,
        layerFileDownloadInfo,
        handleLayerFileDownloadInMemory,
      );
    } else {
      console.error("Information for the layer file to download is missing.");
    }
  };

  return (
    <button
      ref={ref}
      className={cn(
        "flex flex-col items-center justify-center gap-1 px-3 py-2 min-w-12 h-auto text-xs font-medium transition-all duration-200 hover:bg-gray-100 rounded-md cursor-pointer",
        className,
      )}
      onClick={() => onClickDownloadLayerFile()}
      {...props}
    >
      {children}
    </button>
  );
});
LayerFileDownloadItem.displayName = "LayerFileDownloadItem";

export const LayerFileDownload = React.forwardRef<
  HTMLDivElement,
  LayerFileDownloadProps<LayerFileDownloadParam, LayerFileDownloadResult>
>(
  (
    {
      className,
      children,
      layerFileDownloadInfo,
      handleLayerFileDownloadInMemory,
      isLoading,
      ...props
    },
    ref,
  ) => {
    const contextValue: LayerFileDownloadContextValue<
      LayerFileDownloadParam,
      LayerFileDownloadResult
    > = useMemo(
      () => ({
        isLoading,
        layerFileDownloadInfo,
        handleLayerFileDownloadInMemory,
      }),
      [isLoading, layerFileDownloadInfo, handleLayerFileDownloadInMemory],
    );

    return (
      <div ref={ref} className={cn("flex", className)} {...props}>
        <LayerFileDownloadContext.Provider value={contextValue}>
          <HoverCard>{children}</HoverCard>
        </LayerFileDownloadContext.Provider>
      </div>
    );
  },
);
LayerFileDownload.displayName = "LayerFileDownload";

// 완성형 위젯 컴포넌트
export const LayerFileDownloadWidget = ({
  layerFileDownloadInfo,
  activeDownloadFormats = [],
  handleLayerFileDownloadInMemory,
  className,
  buttonName,
}: LayerFileDownloadWidgetProps<
  LayerFileDownloadParam,
  LayerFileDownloadResult
>) => {
  return (
    <LayerFileDownload
      layerFileDownloadInfo={layerFileDownloadInfo}
      handleLayerFileDownloadInMemory={handleLayerFileDownloadInMemory}
      className={className}
    >
      <LayerFileDownloadTrigger buttonName={buttonName}>
        <DownloadIcon className="w-5 h-5" />
      </LayerFileDownloadTrigger>
      <LayerFileDownloadContent>
        {activeDownloadFormats?.map((formatType: LayerFileDownloadFormat) => {
          const formatInfo = LAYER_FILE_DOWNLOAD_FORMATS[formatType];
          const { IconComponent, label, className } = formatInfo;

          return (
            <LayerFileDownloadItem
              key={formatType}
              layerFileDownloadType={formatType}
            >
              <IconComponent className={`w-5 h-5 ${className}`} />
              <span className={className}>{label}</span>
            </LayerFileDownloadItem>
          );
        })}
      </LayerFileDownloadContent>
    </LayerFileDownload>
  );
};
LayerFileDownloadWidget.displayName = "LayerFileDownloadWidget";
