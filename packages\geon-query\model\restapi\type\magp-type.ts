import { JsonResponse } from "utils/geonAPI";

// 각 result 아이템 타입 정의
export interface ResultItem {
  index: number;
  vertexIndex: number;
  targetPoint: string; // e.g. "POINT(127.5 37.5)"
  elevationZ: number;
  demGeometry: string; // e.g. "POINT(127.50001 37.50001)"
  cumulativeDistanceM: number;
}

// 응답 타입 정의
export type ElevationResponse = JsonResponse<ResultItem[]>;

export interface LineStringRequest {
  /** 라인스트링 WKT 문자열 */
  linestringWkt: string;
  /** 목표 좌표계 (EPSG 코드) */
  targetSrid: number;
  /** 샘플링 간격 (단위: m) */
  stepMeters: number;
}

export interface PointStringRequest {
  /** 라인스트링 WKT 문자열 */
  pointWkt: string;
  /** 목표 좌표계 (EPSG 코드) */
  targetSrid: number;
  /** 샘플링 간격 (단위: m) */
  stepMeters: number;
}
