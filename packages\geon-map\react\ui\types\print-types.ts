/** 도메인 타입 */
export type PaperSize =
  | "A4"
  | "A3"
  | "A2"
  | "A1"
  | "A0"
  | "A0-L"
  | "A1-L"
  | "A2-L"
  | "A3-L"
  | "A4-L";
export type CaptureType = "area" | "current-view" | "paper";
export type PrintType = "png" | "pdf";
export type PdfFormat = "a4" | "a3" | "a2" | "a1" | "a0" | "letter";

export interface PaperDimensions {
  width: number; // mm
  height: number; // mm
  label: string;
  ratio: number;
}

export const PAPER_SIZE_INFO: Record<PaperSize, PaperDimensions> = {
  A0: { width: 841, height: 1189, label: "A0 (841×1189mm)", ratio: 841 / 1189 },
  A1: { width: 594, height: 841, label: "A1 (594×841mm)", ratio: 594 / 841 },
  A2: { width: 420, height: 594, label: "A2 (420×594mm)", ratio: 420 / 594 },
  A3: { width: 297, height: 420, label: "A3 (297×420mm)", ratio: 297 / 420 },
  A4: { width: 210, height: 297, label: "A4 (210×297mm)", ratio: 210 / 297 },
  "A0-L": {
    width: 1189,
    height: 841,
    label: "A0-L (1189×841mm)",
    ratio: 1189 / 841,
  },
  "A1-L": {
    width: 841,
    height: 594,
    label: "A1-L (841×594mm)",
    ratio: 841 / 594,
  },
  "A2-L": {
    width: 594,
    height: 420,
    label: "A2-L (594×420mm)",
    ratio: 594 / 420,
  },
  "A3-L": {
    width: 420,
    height: 297,
    label: "A3-L (420×297mm)",
    ratio: 420 / 297,
  },
  "A4-L": {
    width: 297,
    height: 210,
    label: "A4-L (297×210mm)",
    ratio: 297 / 210,
  },
} as const;

/** 용지 크기 라벨만 추출 */
export const PAPER_SIZE_LABELS: Record<PaperSize, string> = {
  A0: PAPER_SIZE_INFO.A0.label,
  A1: PAPER_SIZE_INFO.A1.label,
  A2: PAPER_SIZE_INFO.A2.label,
  A3: PAPER_SIZE_INFO.A3.label,
  A4: PAPER_SIZE_INFO.A4.label,
  "A0-L": PAPER_SIZE_INFO["A0-L"].label,
  "A1-L": PAPER_SIZE_INFO["A1-L"].label,
  "A2-L": PAPER_SIZE_INFO["A2-L"].label,
  "A3-L": PAPER_SIZE_INFO["A3-L"].label,
  "A4-L": PAPER_SIZE_INFO["A4-L"].label,
} as const;
