import {
  ChartArea,
  Droplet,
  DropletOff,
  Fish,
  Route,
  ShieldCheck,
} from "lucide-react";

export type Service = {
  /** route 에 표시될 서비스 이름 (중복 불가) */
  id: string;
  title: string;
  icon: keyof typeof iconMap;
  items?: Omit<Service, "items" | "icon">[];
  [k: string]: any;
};

// 사용 아이콘
export const iconMap = {
  ChartArea,
  Droplet,
  DropletOff,
  Fish,
  Route,
  ShieldCheck,
} as const;

// 서비스 목록
export const SERVICES: Service[] = [
  {
    id: "road",
    title: "도로",
    icon: "Route",
  },
  {
    id: "water",
    title: "상수도",
    icon: "Droplet",
  },
  {
    id: "sewage",
    title: "하수도",
    icon: "DropletOff",
  },
  {
    id: "permit",
    title: "인허가",
    icon: "ShieldCheck",
    items: [
      { id: "permit-1", title: "도로" },
      { id: "permit-2", title: "건물" },
    ],
  },
  {
    id: "ocean",
    title: "해양",
    icon: "Fish",
    items: [
      { id: "ocean-1", title: "어장구역도" },
      { id: "ocean-2", title: "연안 정비 사업" },
    ],
  },
  {
    id: "analysis",
    title: "용도 분석",
    icon: "ChartArea",
  },
];

export const SERVICE_IDS = SERVICES.map((service) => service.id);

// Helper functions
export function getService(id: string): Service | undefined {
  return SERVICES.find((service) => service.id === id);
}

export function getSubService(id: string, subId: string) {
  return SERVICES.find((service) => service.id === id)?.items?.find(
    (sub) => sub.id === subId,
  );
}

export function isValidService(id: string): boolean {
  return SERVICE_IDS.includes(id);
}
