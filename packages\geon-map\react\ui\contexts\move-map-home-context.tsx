import { createContext, useContext } from "react";

import { MoveMapHomeContextValue } from "../types/move-map-home-types";

export const MoveMapHomeContext = createContext<MoveMapHomeContextValue | null>(
  null,
);

// 커스텀 훅
export const useMoveMapHomeContext = () => {
  const context = useContext(MoveMapHomeContext);
  if (!context) {
    throw new Error(
      "MoveMapHomeContext components must be used within MoveMapHome Provider",
    );
  }
  return context;
};
