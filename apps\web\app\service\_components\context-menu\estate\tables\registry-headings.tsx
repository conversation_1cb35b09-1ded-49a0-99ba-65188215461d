"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useFormatter, useTranslations } from "next-intl";
import React from "react";

import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

export default function RegistryHeadings({
  client,
  ...props
}: APIRequestType<EstateClient["registry"]["headings"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.registry.headings");
  const f = useFormatter();
  // Pagination States
  const [numOfRows, setNumOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo, setPageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["registry"]["headings"]>
  >({
    queryKey: ["registry/headings", { ...props, numOfRows, pageNo }],
    queryFn: () => client.registry.headings({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("bcRat", {
      cell: (info) => f.number(Number(info.getValue())),
      header: t("bcRat"),
    }),
    helper.accessor("vlRat", {
      cell: (info) => f.number(Number(info.getValue())),
      header: t("vlRat"),
    }),
    helper.accessor("mainBldCnt", {
      cell: (info) => f.number(Number(info.getValue())),
      header: t("mainBldCnt"),
    }),
    helper.accessor("mainPurpsCdNm", {
      cell: (info) => info.getValue(),
      header: t("mainPurpsCdNm"),
    }),
    helper.accessor("etcPurps", {
      cell: (info) => info.getValue(),
      header: t("etcPurps"),
    }),
  ];

  return (
    <div className="flex w-full flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      {data.result.pageInfo && (
        <Pagination
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
