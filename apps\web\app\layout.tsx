import "@config/tailwind/globals.css";

import { Toaster } from "@geon-ui/react/primitives/sonner";
import { TooltipProvider } from "@geon-ui/react/primitives/tooltip";
import type { Metadata } from "next";
import Script from "next/script";
import { NextIntlClientProvider } from "next-intl";

import { geistMono, geistSans } from "@/assets/fonts";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ko">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        <NextIntlClientProvider>
          {/* TODO provider는 하나로 합치기 */}
          <TooltipProvider>
            <main className="size-full">{children}</main>
            <Toaster />
          </TooltipProvider>
        </NextIntlClientProvider>
        <Script src="/js/odf.min.js" />
      </body>
    </html>
  );
}
