import {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandCharacteristicsTable({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["characteristics"]> & {
  client: EstateClient;
}) {
  // TODO: Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);

  // query
  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["characteristics"]>
  >({
    queryKey: ["land/characteristics", { ...props, numOfRows, pageNo }],
    queryFn: () => client.land.characteristics({ ...props, numOfRows, pageNo }),
  });

  // error handling
  if (isError)
    return (
      <div className="w-full flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">용도지역</TableHead>
            <TableHead className="font-bold text-center">
              용도지역 상세
            </TableHead>
            <TableHead className="font-bold text-center">
              토지 이용 상황
            </TableHead>
            <TableHead className="font-bold text-center">지형 높이</TableHead>
            <TableHead className="font-bold text-center">지형 형상</TableHead>
            <TableHead className="font-bold text-center">도로 접면</TableHead>
            <TableHead className="font-bold text-center">
              공시지가 (원)
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={8} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`land-characteristics-${idx}`}>
                <TableCell className="text-center">
                  {res.prposArea1Nm}
                </TableCell>
                <TableCell className="text-center">
                  {res.prposArea2Nm}
                </TableCell>
                <TableCell className="text-center">
                  {res.ladUseSittnNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.tpgrphHgCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.tpgrphFrmCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.roadSideCodeNm}
                </TableCell>
                <TableCell className="text-right">{res.pblntfPclnd}</TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={8} className="text-center">
                No Data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
