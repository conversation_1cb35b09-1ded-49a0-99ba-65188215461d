import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandOwnershipTable({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["ownership"]> & {
  client: EstateClient;
}) {
  // TODO: Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);

  // query
  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["ownership"]>
  >({
    queryKey: ["land/ownership", { ...props, numOfRows, pageNo }],
    queryFn: () => client.land.ownership({ ...props, numOfRows, pageNo }),
  });

  // error handling
  if (isError)
    return (
      <div className="w-full flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">소유구분</TableHead>
            <TableHead className="font-bold text-center">건물</TableHead>
            <TableHead className="font-bold text-center">거주지 구분</TableHead>
            <TableHead className="font-bold text-center">
              국가 기관 구분
            </TableHead>
            <TableHead className="font-bold text-center">
              소유 변동 원인
            </TableHead>
            <TableHead className="font-bold text-center">
              소유 변동 일자
            </TableHead>
            <TableHead className="font-bold text-center">소유자</TableHead>
            <TableHead className="font-bold text-center">
              공시지가 (원/m<sup>2</sup>)
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={8} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`land-ownership-${idx}`}>
                <TableCell className="text-center">
                  {res.posesnSeCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {`${res.buldDongNm} ${res.buldFloorNm} ${res.buldHoNm} ${res.buldRoomNm}`}
                </TableCell>
                <TableCell className="text-center">
                  {res.resdncSeCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.nationInsttSeCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.ownshipChgCauseCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.ownshipChgDe}
                </TableCell>
                <TableCell className="text-center">{res.ownerNm}</TableCell>
                <TableCell className="text-right">{res.pblntfPclnd}</TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={8} className="text-center">
                No Data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
