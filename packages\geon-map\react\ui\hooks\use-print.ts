"use client";
import {
  useAreaDownload,
  useAreaPrint,
  useMapCapture,
  usePrint as usePrintByODF,
} from "@geon-map/react-odf";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { PAPER_SIZE_INFO, PaperSize, PdfFormat } from "../types/print-types";

export type UseDialogReturn = {
  open: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  onOpenChange: (open: boolean) => void;
};

function useDialogController(
  initial = false,
  onClose?: () => void,
  onOpen?: () => void,
): UseDialogReturn {
  const [open, setOpen] = useState(initial);
  const openDialog = useCallback(() => {
    onOpen?.();
    setOpen(true);
  }, [onOpen]);
  const closeDialog = useCallback(() => {
    onClose?.();
    setOpen(false);
  }, [onClose]);
  const onOpenChange = useCallback(
    (next: boolean) => (next ? openDialog() : closeDialog()),
    [openDialog, closeDialog],
  );
  return { open, openDialog, closeDialog, onOpenChange };
}

export interface UsePrintOptions {
  enableCaptureMode?: boolean;
}

function mmToPx(mm: number, ppi = 96) {
  const inch = mm / 25.4;
  return Math.round(inch * ppi);
}

export function usePrint(options: UsePrintOptions = {}) {
  const { enableCaptureMode = false } = options;

  const [paperSize, setPaperSize] = useState<PaperSize>("A4");
  const [isCapturing, setIsCapturing] = useState(false);

  const { print: printByODF } = usePrintByODF();
  const { printCanvas } = useAreaPrint();
  const { downloadCanvasPdf, downloadCanvasPng } = useAreaDownload();

  // 1) 다이얼로그 훅들 (선행 선언)
  const captureDialogBase = useDialogController(false);
  const paperDialog = useDialogController(false);
  const originalDialog = useDialogController(false);

  // 2) 캡처 훅 (onCapture에서 captureDialogBase 참조)
  const {
    startCapturing,
    stopCapturing,
    clearCapture,
    captureCurrentMap,
    captureResult,
    resetCapture,
  } = useMapCapture({
    onCapture: () => {
      if (!isMountedRef.current) return;
      captureDialogBase.openDialog();
      stopCapturing();
      clearCapture();
      setIsCapturing(false);
    },
  });

  // 3) captureDialog 닫을 때 resetCapture가 자동 실행되도록 래핑
  const captureDialog = useMemo<UseDialogReturn>(() => {
    return {
      open: captureDialogBase.open,
      openDialog: captureDialogBase.openDialog,
      closeDialog: () => {
        try {
          resetCapture();
        } catch (e) {
          console.warn("resetCapture failed:", e);
        }
        captureDialogBase.closeDialog();
      },
      onOpenChange: (next: boolean) => {
        if (next) {
          captureDialogBase.onOpenChange(true);
        } else {
          try {
            resetCapture();
          } catch (e) {
            console.warn("resetCapture failed:", e);
          }
          captureDialogBase.onOpenChange(false);
        }
      },
    };
  }, [captureDialogBase, resetCapture]);

  // 4) 언마운트/전환 가드
  const isMountedRef = useRef(true);
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
      try {
        stopCapturing();
        clearCapture();
      } catch {
        /* noop */
      }
    };
  }, [stopCapturing, clearCapture]);

  // dialogs 합성
  const dialogs = useMemo(
    () => ({
      capture: captureDialog,
      paper: paperDialog,
      original: originalDialog,
    }),
    [captureDialog, paperDialog, originalDialog],
  );

  // 영역 캡처 시작: 다이얼로그 닫고 → 캡처 시작 (중복 방지)
  const startArea = useCallback(() => {
    if (isCapturing) return;
    setIsCapturing(true);
    captureDialog.closeDialog();
    startCapturing();
  }, [isCapturing, captureDialog, startCapturing]);

  // 현재 화면 캡처
  const captureCurrentView = useCallback(async () => {
    await captureCurrentMap();
  }, [captureCurrentMap]);

  // 용지별 캡처 시작: 메인 다이얼로그 닫고 용지 다이얼로그 열기
  const startPaperCapture = useCallback(() => {
    captureDialog.closeDialog();
    paperDialog.openDialog();
  }, [captureDialog, paperDialog]);

  // 캡처 결과 PNG 저장
  const saveCapturedPng = useCallback(async () => {
    if (!captureResult?.canvas) return false;
    try {
      await downloadCanvasPng(captureResult.canvas);
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  }, [downloadCanvasPng, captureResult]);

  // 임의 캔버스 PNG 저장
  const saveCanvasPng = useCallback(
    async (canvas: HTMLCanvasElement) => {
      try {
        await downloadCanvasPng(canvas);
        return true;
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    [downloadCanvasPng],
  );

  // 캡처 결과 PDF 저장
  const saveCapturedPdf = useCallback(async () => {
    if (!captureResult?.canvas) return false;
    try {
      await downloadCanvasPdf(captureResult.canvas, {
        orientation: paperSize.includes("-L") ? "landscape" : "portrait",
        format: paperSize.toLowerCase().split("-l")[0] as PdfFormat,
      });
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  }, [downloadCanvasPdf, captureResult, paperSize]);

  // 임의 캔버스 PDF 저장
  const saveCanvasPdf = useCallback(
    async (canvas: HTMLCanvasElement) => {
      try {
        await downloadCanvasPdf(canvas, {
          orientation: paperSize.includes("-L") ? "landscape" : "portrait",
          format: paperSize.toLowerCase().split("-l")[0] as PdfFormat,
        });
        return true;
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    [downloadCanvasPdf, paperSize],
  );

  // 캡처 결과 프린트
  const printCaptured = useCallback(async () => {
    if (!captureResult?.canvas) return false;
    try {
      await printCanvas(captureResult.canvas, { paperSize });
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  }, [captureResult, printCanvas, paperSize]);

  // 임의 캔버스 프린트
  const printCanvasDirect = useCallback(
    async (canvas: HTMLCanvasElement) => {
      try {
        await printCanvas(canvas, { paperSize });
        return true;
      } catch (e) {
        console.error(e);
        return false;
      }
    },
    [printCanvas, paperSize],
  );

  // 프린트 버튼 (캡처모드면 다이얼로그, 아니면 바로 인쇄)
  const handlePrintClick = useCallback(() => {
    if (enableCaptureMode) {
      captureDialog.openDialog();
    } else {
      printByODF();
    }
  }, [enableCaptureMode, captureDialog, printByODF]);

  // 용지 크기 변경
  const changePaperSize = useCallback((size: PaperSize) => {
    setPaperSize(size);
  }, []);

  // 미리보기 컨테이너 사이즈(px) 계산 (디스플레이 PPI 주입 가능)
  const getPreviewSize = useCallback((size: PaperSize, ppi = 96) => {
    const meta = PAPER_SIZE_INFO[size];
    return {
      widthPx: mmToPx(meta.width, ppi),
      heightPx: mmToPx(meta.height, ppi),
      ratio: meta.ratio,
    };
  }, []);

  // 원본 미리보기 오픈 (필요 시 사용)
  const openOriginalPreview = useCallback(() => {
    if (!captureResult?.canvas) return;
    originalDialog.openDialog();
  }, [captureResult, originalDialog]);

  // 기존 함수는 유지하고 새로운 DPI 함수 추가
  const getPaperSizeInPixels = useCallback(
    (
      paperSize: PaperSize,
      dpi: number = 300,
      includeMargin: boolean = true,
      marginMm: number = 10,
    ) => {
      const paperInfo = PAPER_SIZE_INFO[paperSize];
      const mmToInch = 1 / 25.4;

      // 전체 용지 크기 (픽셀)
      const fullWidthPx = Math.round(paperInfo.width * mmToInch * dpi);
      const fullHeightPx = Math.round(paperInfo.height * mmToInch * dpi);

      if (!includeMargin) {
        return {
          width: fullWidthPx,
          height: fullHeightPx,
          fullWidth: fullWidthPx,
          fullHeight: fullHeightPx,
          marginPx: 0,
          dpi,
          paperSize,
        };
      }

      // 마진 제외한 인쇄 가능 영역
      const marginPx = Math.round(marginMm * mmToInch * dpi);
      const printableWidthPx = fullWidthPx - marginPx * 2;
      const printableHeightPx = fullHeightPx - marginPx * 2;

      return {
        width: printableWidthPx,
        height: printableHeightPx,
        fullWidth: fullWidthPx,
        fullHeight: fullHeightPx,
        marginPx,
        dpi,
        paperSize,
        // 추가 정보
        widthMm: paperInfo.width,
        heightMm: paperInfo.height,
        printableWidthMm: paperInfo.width - marginMm * 2,
        printableHeightMm: paperInfo.height - marginMm * 2,
      };
    },
    [],
  );

  return {
    // 다이얼로그 컨트롤
    dialogs, // { capture, paper, original }

    // 상태
    currentPaperSize: paperSize,
    isCapturing,
    captureResult,

    // 유틸/계산기
    getPreviewSize,
    getPaperSizeInPixels,

    // 액션
    changePaperSize,
    startArea,
    captureCurrentView,
    startPaperCapture,
    saveCapturedPng,
    saveCanvasPng,
    saveCapturedPdf,
    saveCanvasPdf,
    printCanvas: printCanvasDirect,
    printCaptured,
    handlePrintClick,
    openOriginalPreview,
  };
}
