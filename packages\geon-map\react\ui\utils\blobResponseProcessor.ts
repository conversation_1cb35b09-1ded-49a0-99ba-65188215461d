/** ### blob 응답의 content-disposition 헤더 문자열의 파일 이름 추출 유틸
 * ---
 * - 현재 서버에서 노출 시킨 헤더는 content-length 뿐이라 content-disposition 접근 불가
 * @Todo headers.get("content-disposition") 가능하도록 변경필요(API 단).
 * @param headers Response Header
 * @returns 파일 이름 문자열 또는 null
 */
export const getFilenameFromHeader = (headers: Headers) => {
  const contentDisposition = headers.get("content-disposition");

  if (!contentDisposition) {
    return null;
  }

  // "filename=" 또는 "filename*=" 패턴 추출을 위한 정규식
  const filenameMatch =
    /filename\*?=['"]?(?:UTF-\d{1,2}''|..)?([^'";\r\n]*)/.exec(
      contentDisposition,
    );
  if (filenameMatch && filenameMatch[1]) {
    try {
      // URL 인코딩된 파일명 디코딩
      return decodeURIComponent(filenameMatch[1]);
    } catch (error) {
      console.error("Layer Filename decoding failed", error);
      return filenameMatch[1];
    }
  }

  return null;
};

/** ### 메모리의 blob 응답 파일 다운로드 유틸 */
export const downloadBlobFile = (fileName: string, blob: Blob) => {
  // 메모리에 있는 Blob 데이터를 가리키는 임시 URL을 생성
  const url = window.URL.createObjectURL(blob);

  // 다운로드를 실행을 위한 보이지 않는 <a> 태그 생성
  const link = document.createElement("a");
  link.href = url;

  // download 속성에 파일 이름 지정
  link.setAttribute("download", fileName);

  // <a> 태그를 추가하고 강제로 클릭하여 다운로드
  document.body.appendChild(link);
  link.click();

  // 다운로드 시작 후, 임시 태그와 URL 삭제
  link.remove();
  window.URL.revokeObjectURL(url);
};
