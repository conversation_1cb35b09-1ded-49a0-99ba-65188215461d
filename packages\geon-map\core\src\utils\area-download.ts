import { AreaDownloadOptions, PdfOptions } from "../types/area-download";

export class AreaDownload {
  private options: AreaDownloadOptions;

  constructor(options: AreaDownloadOptions = {}) {
    this.options = {
      filename: "download",
      quality: 1.0,
      backgroundColor: "#ffffff",
      pixelRatio: 2,
      ...options,
      pdf: {
        orientation: "portrait",
        format: "a4",
        fitToPage: true,
        margin: 10,
        ...options.pdf,
      },
    };
  }

  /**
   * HTML 요소를 PNG 이미지로 다운로드합니다
   */
  async downloadPng(element?: HTMLElement | string): Promise<void> {
    try {
      const targetElement = this.getTargetElement(
        element || this.options.element,
      );
      if (!targetElement) {
        throw new Error("다운로드할 요소를 찾을 수 없습니다.");
      }

      // html-to-image 동적 임포트
      const htmlToImage = await this.loadHtmlToImage();

      // 이미지로 변환
      const dataUrl = await htmlToImage.toPng(targetElement, {
        quality: this.options.quality,
        backgroundColor: this.options.backgroundColor,
        pixelRatio: this.options.pixelRatio,
        width: this.options.size?.width,
        height: this.options.size?.height,
      });

      // 다운로드 실행
      this.downloadFile(dataUrl, `${this.options.filename}.png`);
    } catch (error) {
      console.error("PNG 다운로드 중 오류:", error);
      throw error;
    }
  }

  /**
   * HTML 요소를 PDF로 다운로드합니다
   */
  async downloadPdf(
    element?: HTMLElement | string,
    pdfOverride?: PdfOptions,
  ): Promise<void> {
    try {
      const targetElement = this.getTargetElement(
        element || this.options.element,
      );
      if (!targetElement) {
        throw new Error("다운로드할 요소를 찾을 수 없습니다.");
      }

      // 먼저 PNG로 변환
      const htmlToImage = await this.loadHtmlToImage();
      const imageDataUrl = await htmlToImage.toPng(targetElement, {
        quality: this.options.quality,
        backgroundColor: this.options.backgroundColor,
        pixelRatio: this.options.pixelRatio,
        width: this.options.size?.width,
        height: this.options.size?.height,
      });

      await this.createPdfFromDataUrl(
        imageDataUrl,
        undefined,
        undefined,
        pdfOverride,
      );
    } catch (error) {
      console.error("PDF 다운로드 중 오류:", error);
      throw error;
    }
  }

  /**
   * 캔버스를 PNG 이미지로 다운로드합니다
   */
  async downloadCanvasPng(canvas: HTMLCanvasElement): Promise<void> {
    try {
      // 캔버스를 데이터 URL로 변환
      const dataUrl = canvas.toDataURL("image/png", this.options.quality);

      // 다운로드 실행
      this.downloadFile(dataUrl, `${this.options.filename}.png`);
    } catch (error) {
      console.error("캔버스 PNG 다운로드 중 오류:", error);
      throw error;
    }
  }

  /**
   * 캔버스를 PDF로 다운로드합니다
   */
  /**
   * 캔버스를 PDF로 다운로드합니다
   */
  async downloadCanvasPdf(
    canvas: HTMLCanvasElement,
    pdfOverride?: PdfOptions,
  ): Promise<void> {
    try {
      // 캔버스를 데이터 URL로 변환
      const dataUrl = canvas.toDataURL("image/png", this.options.quality);

      await this.createPdfFromDataUrl(
        dataUrl,
        canvas.width,
        canvas.height,
        pdfOverride,
      );
    } catch (error) {
      console.error("캔버스 PDF 다운로드 중 오류:", error);
      throw error;
    }
  }

  /**
   * 다운로드 옵션을 업데이트합니다
   */
  updateOptions(options: Partial<AreaDownloadOptions>): void {
    this.options = {
      ...this.options,
      ...options,
      pdf: {
        ...this.options.pdf,
        ...options.pdf,
      },
    };
  }

  private getTargetElement(element?: HTMLElement | string): HTMLElement | null {
    if (!element) return null;

    if (typeof element === "string") {
      return document.querySelector(element);
    }

    return element;
  }

  private async loadHtmlToImage() {
    if (typeof window === "undefined") {
      throw new Error(
        "html-to-image는 브라우저 환경에서만 사용할 수 있습니다.",
      );
    }

    return await import("html-to-image");
  }

  private async loadJsPDF() {
    if (typeof window === "undefined") {
      throw new Error("jsPDF는 브라우저 환경에서만 사용할 수 있습니다.");
    }

    const { jsPDF } = await import("jspdf");
    return jsPDF;
  }

  private async createPdfFromDataUrl(
    imageDataUrl: string,
    sourceWidth?: number,
    sourceHeight?: number,
    pdfOverride?: PdfOptions,
  ): Promise<void> {
    // jsPDF 동적 임포트
    const jsPDF = await this.loadJsPDF();

    // 호출 단위 오버라이드 병합 (호출값 > 전역 기본값)
    const pdfOptions: Required<PdfOptions> = {
      orientation: this.options.pdf!.orientation ?? "portrait",
      format: this.options.pdf!.format ?? "a4",
      fitToPage: this.options.pdf!.fitToPage ?? true,
      margin: this.options.pdf!.margin ?? 10,
      ...pdfOverride, // override가 있으면 해당 필드만 덮어씀
    };

    // PDF 문서 생성
    const pdf = new jsPDF({
      orientation: pdfOptions.orientation,
      unit: "mm",
      format: pdfOptions.format,
    });

    // 페이지 크기 가져오기
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // 여백 적용
    const margin = pdfOptions.margin;
    const availableWidth = pageWidth - margin * 2;
    const availableHeight = pageHeight - margin * 2;

    let imgWidth = availableWidth;
    let imgHeight = availableHeight;

    if (pdfOptions.fitToPage) {
      if (sourceWidth && sourceHeight) {
        // 캔버스의 경우 직접 크기 사용
        const ratio = Math.min(
          availableWidth / sourceWidth,
          availableHeight / sourceHeight,
        );
        imgWidth = sourceWidth * ratio;
        imgHeight = sourceHeight * ratio;
      } else {
        // HTML 요소의 경우 이미지를 로드하여 크기 정보 가져오기
        const img = new Image();
        await new Promise<void>((resolve, reject) => {
          img.onload = () => resolve();
          img.onerror = reject;
          img.src = imageDataUrl;
        });

        const ratio = Math.min(
          availableWidth / img.width,
          availableHeight / img.height,
        );
        imgWidth = img.width * ratio;
        imgHeight = img.height * ratio;
      }
    }

    // 중앙 정렬 위치
    const x = margin + (availableWidth - imgWidth) / 2;
    const y = margin + (availableHeight - imgHeight) / 2;

    // PDF에 이미지 추가
    pdf.addImage(imageDataUrl, "PNG", x, y, imgWidth, imgHeight);

    // PDF 다운로드
    pdf.save(`${this.options.filename}.pdf`);
  }

  private downloadFile(dataUrl: string, filename: string): void {
    const link = document.createElement("a");
    link.download = filename;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 요소의 크기를 자동으로 감지합니다
   */
  static getElementSize(element: HTMLElement): {
    width: number;
    height: number;
  } {
    const rect = element.getBoundingClientRect();
    return {
      width: rect.width,
      height: rect.height,
    };
  }

  /**
   * 다운로드 전에 요소가 완전히 로드되었는지 확인합니다
   */
  static async waitForElementLoad(
    element: HTMLElement,
    timeout: number = 5000,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const images = element.querySelectorAll("img");
      let loadedCount = 0;
      const totalCount = images.length;

      if (totalCount === 0) {
        resolve();
        return;
      }

      const timeoutId = setTimeout(() => {
        reject(new Error("요소 로드 시간 초과"));
      }, timeout);

      const checkComplete = () => {
        loadedCount++;
        if (loadedCount >= totalCount) {
          clearTimeout(timeoutId);
          resolve();
        }
      };

      images.forEach((img) => {
        if (img.complete) {
          checkComplete();
        } else {
          img.onload = checkComplete;
          img.onerror = checkComplete;
        }
      });
    });
  }
}
