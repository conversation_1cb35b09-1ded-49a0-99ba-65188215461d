import { type ClassValue, clsx } from "clsx";
import { createLucideIcon } from "lucide-react";
import React from "react";
import { twMerge } from "tailwind-merge";

/**
 * Tailwindcss 의 className 을 합쳐주는 utility 함수.
 * 중복되는 속성은 뒤에 오는 className 이 덮어씁니다.
 *
 * @example
 * ```tsx
 * function Component({ className }: ComponentProps) {
 *   return (
 *     <div className={cn("p-5", className)} />
 *   );
 * }
 *
 * <Component className="p-10" />
 * // This equals to:
 * <div className="p-10" />
 * ```
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * svg 파일을 lucide-react icon 형태로 변환
 *
 * @param iconName
 * @param svg
 */
export function createIcon(
  iconName: string,
  svg: React.FC,
): ReturnType<typeof createLucideIcon> {
  return createLucideIcon(iconName, createIconNode(svg));
}

type SVGElementType =
  | "circle"
  | "ellipse"
  | "g"
  | "line"
  | "path"
  | "polygon"
  | "polyline"
  | "rect";

type IconNode = [elementName: SVGElementType, attrs: Record<string, string>][];

const createIconNode = (svg: React.FC) => {
  const node: IconNode = [];
  const element: React.ReactNode | Promise<React.ReactNode> = svg({});
  const children: React.ReactElement<any, any>[] = (
    element as React.ReactElement<any, any>
  ).props.children;

  children.forEach((child, index) => {
    node.push([child.type, { ...child.props, key: `icon-node-${index}` }]);
  });

  return node;
};
