"use client";

import { BasemapInfo } from "@geon-map/core";
import { useLayer } from "@geon-map/react-odf";
import {
  Basemap,
  BasemapContent,
  BasemapItem,
  BasemapTrigger,
  DEFAULT_BASE_MAPS,
  hybridLayerOption,
} from "@geon-map/react-ui/components";
import { basemapWidget as basemapConverter } from "@geon-map/react-ui/utils";
import { BasemapListRequest, defaultGeonSmtClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { cn } from "@geon-ui/react/lib/utils";
import { Checkbox } from "@geon-ui/react/primitives/checkbox";
import { MapIcon } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

type GroupedBasemaps = Record<string, BasemapInfo[]>;

interface BasemapWidgetUseProps {
  className?: string;
  hybrid?: boolean;
}

export default function BasemapWidgetUse({
  className = "absolute right-4 top-20 flex flex-col gap-2",
  hybrid = true,
}: BasemapWidgetUseProps) {
  const [value, onValueChange] = useState<BasemapInfo | undefined>(
    DEFAULT_BASE_MAPS[1],
  );
  const [hybridEnabled, setHybridEnabled] = useState<boolean>(!!hybrid);
  const hybridLayerIdRef = useRef<string | null>(null);
  const { addLayer, setVisible } = useLayer();
  const [grouped, setGrouped] = useState<GroupedBasemaps>({});
  const [selectedGroup, setSelectedGroup] = useState<string>("");

  const [params] = useState<BasemapListRequest>({
    pageIndex: 1,
    pageSize: 100,
  });
  // 체크박스로 ON/OFF
  const handleHybridToggle = useCallback(
    async (checked: boolean) => {
      setHybridEnabled(!!checked);
      const id = hybridLayerIdRef.current;
      if (id) {
        setVisible(id, !!checked);
      } else if (checked) {
        // 아직 추가되지 않은 상태에서 켜는 경우 한 번만 추가
        const newId = await addLayer({
          ...hybridLayerOption,
          ...{ visible: hybrid },
        });
        hybridLayerIdRef.current = String(newId);
        setVisible(hybridLayerIdRef.current, true);
      }
    },
    [addLayer, setVisible],
  );

  const { data, isLoading } = useAppQuery({
    queryKey: ["basemapList", params],
    queryFn: () => defaultGeonSmtClient.basemap.list(params),
  });

  // 최초 마운트 시, hybrid가 true면 레이어 한 번 추가
  useEffect(() => {
    let mounted = true;
    if (hybrid && hybridLayerIdRef.current === null) {
      addLayer(hybridLayerOption).then((layerId) => {
        if (!mounted) return;
        hybridLayerIdRef.current = String(layerId);
        // 초기값이 true면 바로 보이도록
        setVisible(hybridLayerIdRef.current, true);
      });
    }
    return () => {
      mounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 변환 + 그룹 상태 세팅
  useEffect(() => {
    if (!isLoading && data?.result) {
      const groupedResult = basemapConverter(
        data.result.list,
      ) as GroupedBasemaps;
      setGrouped(groupedResult);

      // 첫 로딩 시 첫 그룹을 기본 선택
      const keys = Object.keys(groupedResult);
      if (keys.length && !selectedGroup) {
        setSelectedGroup(keys[0]!);
      }
    }
  }, [isLoading, data, selectedGroup]);

  const groupKeys = useMemo(() => Object.keys(grouped), [grouped]);
  const currentItems = useMemo(
    () => (selectedGroup ? (grouped[selectedGroup] ?? []) : []),
    [grouped, selectedGroup],
  );

  return (
    <Basemap
      basemapInfo={value}
      onValueChange={onValueChange}
      className={cn("absolute right-4 top-20 flex flex-col gap-2", className)}
    >
      <BasemapTrigger className="bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800">
        <MapIcon />
      </BasemapTrigger>

      <BasemapContent>
        {/* 상단 바: 그룹 선택 + 하이브리드 체크박스 */}
        <div className="sticky top-0 z-10 mb-2 flex items-center gap-3 bg-white/80 p-2 backdrop-blur-sm dark:bg-zinc-900/70">
          <select
            className="w-full rounded-md border border-zinc-300 bg-white px-2 py-1 text-sm outline-none focus:ring dark:border-zinc-700 dark:bg-zinc-800"
            value={selectedGroup}
            onChange={(e) => setSelectedGroup(e.target.value)}
            disabled={groupKeys.length === 0}
          >
            {groupKeys.map((key) => (
              <option key={key} value={key}>
                {key || "기타"}
              </option>
            ))}
          </select>

          {/* Select 오른쪽에 하이브리드 체크박스 */}
          <label className="flex items-center gap-2 text-xs text-zinc-700 dark:text-zinc-300">
            <Checkbox
              checked={hybridEnabled}
              onCheckedChange={(v) => handleHybridToggle(!!v)}
              aria-label="하이브리드 지도"
            />
            하이브리드
          </label>
        </div>

        {/* 베이스맵 리스트 */}
        {currentItems.map((map, index) => (
          <BasemapItem key={`${map.bcrnMapNm}-${index}`} basemapInfo={map}>
            <div className="h-[30px]items-center flex gap-3 rounded-md p-1.5 transition-colors duration-200 hover:bg-white/70 dark:hover:bg-zinc-800/60">
              <div className="rounded-lg p-1.5">
                {map["base64"] ? (
                  <img
                    src={map["base64"] as unknown as string}
                    alt={map.bcrnMapNm}
                    className="h-[50px] w-[50px] object-contain"
                  />
                ) : (
                  <div className="flex h-[50px] w-[50px] items-center justify-center rounded bg-zinc-100 text-[10px] text-zinc-500 dark:bg-zinc-700 dark:text-zinc-300">
                    NO IMG
                  </div>
                )}
              </div>
              <div className="flex flex-col items-start gap-0.5">
                <span className="font-medium">{map.bcrnMapNm}</span>
                <span className="text-[11px] leading-tight">
                  {map.bcrnMapClCodeNm}
                </span>
              </div>
            </div>
          </BasemapItem>
        ))}
      </BasemapContent>
    </Basemap>
  );
}
