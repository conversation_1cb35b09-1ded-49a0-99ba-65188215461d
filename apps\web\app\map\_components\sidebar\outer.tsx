import {
  Sidebar,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>barHeader,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import HomeButton from "./home-button";
import NavMap from "./nav-map";
import NavUser from "./nav-user";

// This is sample data.
const data = {
  user: {
    name: "user1",
    role: "Administrator",
    avatar: "/avatars/shadcn.jpg",
  },
};

export default function OuterSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props} collapsible="icon" className="z-50">
      <SidebarHeader>
        <HomeButton />
      </SidebarHeader>
      <SidebarContent>
        <NavMap />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
