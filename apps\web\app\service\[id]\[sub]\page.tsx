import { notFound } from "next/navigation";

import { getSubService, SERVICES } from "../../_utils";

export async function generateStaticParams() {
  return SERVICES.flatMap(
    (service) =>
      service.items?.map((sub) => ({
        id: service.id,
        sub: sub.id,
      })) || [],
  );
}

export default async function Page({
  params,
}: {
  params: Promise<{ id: string; sub: string }>;
}) {
  const { id, sub } = await params;
  const service = getSubService(id, sub);

  // 정의된 서비스가 아니면 404 return
  if (!service) {
    notFound();
  }

  // TODO: Auth Check

  return (
    <div className="flex size-full items-center justify-center">
      {id}/{service.id} page
    </div>
  );
}
