"use client";

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
} from "@geon-ui/react/primitives/sidebar";
import { useEffect } from "react";

import {
  FacilitySearchProvider,
  useFacilitySearch,
} from "../../_contexts/facility-search";
import { useServiceSidebar } from "../../_contexts/sidebar";
import { FacilitySearchContent } from "../facility/facility-search-content";
import { FacilitySearchHeader } from "../facility/facility-search-header";

function InnerSidebarWithFacilitySearch() {
  const { selectedServiceId } = useServiceSidebar();
  const { selectService } = useFacilitySearch();

  // 선택된 서비스가 변경되면 FacilitySearch Context에 알림
  useEffect(() => {
    if (selectedServiceId) {
      selectService(selectedServiceId);
    }
  }, [selectedServiceId, selectService]);

  return (
    <Sidebar className="left-(--sidebar-width-icon)" variant="inset">
      <SidebarHeader className="shrink-0 border-b p-0">
        <FacilitySearchHeader />
      </SidebarHeader>

      <SidebarContent className="p-0">
        <FacilitySearchContent />
      </SidebarContent>

      <SidebarRail />
    </Sidebar>
  );
}

export default function InnerSidebar() {
  const { selectedServiceId } = useServiceSidebar();

  // 서비스가 선택되지 않은 경우
  if (!selectedServiceId) {
    return (
      <Sidebar className="left-(--sidebar-width-icon)" variant="inset">
        <SidebarHeader className="shrink-0 border-b px-4 py-3">
          <div className="text-muted-foreground text-sm font-semibold">
            서비스를 선택하세요
          </div>
        </SidebarHeader>

        <SidebarContent>
          <div className="flex h-full items-center justify-center">
            <p className="text-muted-foreground text-sm">
              좌측 메뉴에서 서비스를 선택해주세요
            </p>
          </div>
        </SidebarContent>

        <SidebarRail />
      </Sidebar>
    );
  }

  return (
    <FacilitySearchProvider>
      <InnerSidebarWithFacilitySearch />
    </FacilitySearchProvider>
  );
}
