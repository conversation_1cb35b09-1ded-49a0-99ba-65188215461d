import { AreaDownload, AreaDownloadOptions, PdfOptions } from "@geon-map/core";
import { useCallback, useRef, useState } from "react";

export interface UseAreaDownloadOptions extends AreaDownloadOptions {
  /** 자동으로 인스턴스를 생성할지 여부 */
  autoCreate?: boolean;
  /** 다운로드 완료 콜백 */
  onDownloadComplete?: (type: "png" | "pdf", filename: string) => void;
  /** 다운로드 실패 콜백 */
  onDownloadError?: (error: Error, type: "png" | "pdf") => void;
}

// CaptureResult 타입 추가
export interface CaptureResult {
  canvas: HTMLCanvasElement;
  rect: any; // CaptureRect 타입
  rectPx: any; // CaptureRect 타입
}

export interface UseAreaDownloadReturn {
  /** PNG 다운로드 */
  downloadPng: (element?: HTMLElement | string) => Promise<void>;
  /** PDF 다운로드 */
  downloadPdf: (
    element?: HTMLElement | string,
    pdf?: PdfOptions,
  ) => Promise<void>;
  /** 캔버스를 PNG로 다운로드 */
  downloadCanvasPng: (canvas: HTMLCanvasElement) => Promise<void>;
  /** 캔버스를 PDF로 다운로드 */
  downloadCanvasPdf: (
    canvas: HTMLCanvasElement,
    pdf?: PdfOptions,
  ) => Promise<void>;
  /** 캡처된 결과를 PNG로 다운로드 */
  downloadCapturedPng: (captureResult: CaptureResult) => Promise<void>;
  /** 캡처된 결과를 PDF로 다운로드 */
  downloadCapturedPdf: (
    captureResult: CaptureResult,
    pdf?: PdfOptions,
  ) => Promise<void>;
  /** 다운로드 옵션 업데이트 */
  updateOptions: (options: Partial<AreaDownloadOptions>) => void;
  /** 요소 로딩 대기 */
  waitForElementLoad: (element: HTMLElement, timeout?: number) => Promise<void>;
  /** 요소 크기 가져오기 */
  getElementSize: (element: HTMLElement) => { width: number; height: number };
  /** 현재 다운로드 중인지 여부 */
  isDownloading: boolean;
  /** 마지막 에러 */
  error: Error | null;
}

export function useAreaDownload(
  options: UseAreaDownloadOptions = {},
): UseAreaDownloadReturn {
  const {
    autoCreate = true,
    onDownloadComplete,
    onDownloadError,
    ...downloadOptions
  } = options;

  const areaDownloadOptions = downloadOptions as AreaDownloadOptions;

  const areaDownloadRef = useRef<AreaDownload | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // AreaDownload 인스턴스 생성
  const getAreaDownload = useCallback(() => {
    if (!areaDownloadRef.current && autoCreate) {
      areaDownloadRef.current = new AreaDownload(areaDownloadOptions);
    }
    return areaDownloadRef.current;
  }, [autoCreate, areaDownloadOptions]);

  // 공통 다운로드 실행 헬퍼
  const executeDownload = useCallback(
    async (downloadFn: () => Promise<void>, type: "png" | "pdf") => {
      const areaDownload = getAreaDownload();
      if (!areaDownload) {
        const error = new Error("AreaDownload 인스턴스가 생성되지 않았습니다.");
        setError(error);
        onDownloadError?.(error, type);
        throw error;
      }

      setIsDownloading(true);
      setError(null);

      try {
        await downloadFn();
        const filename = `${areaDownloadOptions.filename || "download"}.${type}`;
        onDownloadComplete?.(type, filename);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        onDownloadError?.(error, type);
        throw error;
      } finally {
        setIsDownloading(false);
      }
    },
    [
      getAreaDownload,
      areaDownloadOptions.filename,
      onDownloadComplete,
      onDownloadError,
    ],
  );

  // PNG 다운로드
  const downloadPng = useCallback(
    async (element?: HTMLElement | string) => {
      const areaDownload = getAreaDownload()!;
      await executeDownload(() => areaDownload.downloadPng(element), "png");
    },
    [executeDownload, getAreaDownload],
  );

  // PDF 다운로드
  const downloadPdf = useCallback(
    async (element?: HTMLElement | string, pdf?: PdfOptions) => {
      const areaDownload = getAreaDownload()!;
      await executeDownload(
        () => areaDownload.downloadPdf(element, pdf),
        "pdf",
      );
    },
    [executeDownload, getAreaDownload],
  );

  // 캔버스 PNG 다운로드
  const downloadCanvasPng = useCallback(
    async (canvas: HTMLCanvasElement) => {
      const areaDownload = getAreaDownload()!;
      await executeDownload(
        () => areaDownload.downloadCanvasPng(canvas),
        "png",
      );
    },
    [executeDownload, getAreaDownload],
  );

  // 캔버스 PDF 다운로드
  const downloadCanvasPdf = useCallback(
    async (canvas: HTMLCanvasElement, pdf?: PdfOptions) => {
      const areaDownload = getAreaDownload()!;
      await executeDownload(
        () => areaDownload.downloadCanvasPdf(canvas, pdf),
        "pdf",
      );
    },
    [executeDownload, getAreaDownload],
  );

  // 캡처된 결과 PNG 다운로드 (편의 메서드)
  const downloadCapturedPng = useCallback(
    async (captureResult: CaptureResult) => {
      await downloadCanvasPng(captureResult.canvas);
    },
    [downloadCanvasPng],
  );

  // 캡처된 결과 PDF 다운로드 (편의 메서드)
  const downloadCapturedPdf = useCallback(
    async (captureResult: CaptureResult, pdf?: PdfOptions) => {
      await downloadCanvasPdf(captureResult.canvas, pdf);
    },
    [downloadCanvasPdf],
  );

  // 옵션 업데이트
  const updateOptions = useCallback(
    (newOptions: Partial<AreaDownloadOptions>) => {
      const areaDownload = getAreaDownload();
      if (areaDownload) {
        areaDownload.updateOptions(newOptions);
      }
    },
    [getAreaDownload],
  );

  // 요소 로딩 대기 (정적 메서드 래핑)
  const waitForElementLoad = useCallback(
    async (element: HTMLElement, timeout?: number) => {
      return AreaDownload.waitForElementLoad(element, timeout);
    },
    [],
  );

  // 요소 크기 가져오기 (정적 메서드 래핑)
  const getElementSize = useCallback((element: HTMLElement) => {
    return AreaDownload.getElementSize(element);
  }, []);

  return {
    downloadPng,
    downloadPdf,
    downloadCanvasPng,
    downloadCanvasPdf,
    downloadCapturedPng,
    downloadCapturedPdf,
    updateOptions,
    waitForElementLoad,
    getElementSize,
    isDownloading,
    error,
  };
}
