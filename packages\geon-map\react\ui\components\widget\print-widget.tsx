"use client";

import type { CaptureResult } from "@geon-map/core";
import { useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  HoverCard,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import {
  Camera,
  Download,
  Eye,
  Printer,
  Square,
  StickyNote,
} from "lucide-react";
import React, { forwardRef, useEffect, useState } from "react";

import { type UseDialogReturn, usePrint } from "../../hooks";
import type {
  CaptureType,
  PaperSize,
  PrintType,
} from "../../types/print-types";
import { PAPER_SIZE_LABELS } from "../../types/print-types"; // ✅ 값 import

interface PrintContextValue {
  enableCaptureMode: boolean;

  // 상태
  currentPaperSize: PaperSize;
  captureResult?: CaptureResult | null;
  isCapturing: boolean;
  // 다이얼로그 컨트롤
  dialogs: {
    capture: UseDialogReturn;
    paper: UseDialogReturn;
    original: UseDialogReturn;
  };

  getPaperSizeInPixels: (
    paperSize: PaperSize,
    dpi?: number,
    includeMargin?: boolean,
    marginMm?: number,
  ) => {
    width: number;
    height: number;
    fullWidth: number;
    fullHeight: number;
    marginPx: number;
    dpi: number;
    paperSize: PaperSize;
  };

  // 훅 액션
  // handlePrint: () => void;
  // handleAreaCapture: () => void;
  // handleCurrentViewCapture: () => void;
  // handlePaperCapture: () => void;
  // handleSavePng: () => void;
  // handleSavePdf: () => void;
  // handlePrintAction: () => void;

  // 버튼 액션
  changePaperSize: (size: PaperSize) => void;
  startArea: () => void;
  captureCurrentView: () => void;
  startPaperCapture: () => void;
  saveCapturedPng: () => Promise<boolean>;
  saveCanvasPng: (canvas: HTMLCanvasElement) => Promise<boolean>;
  saveCapturedPdf: () => Promise<boolean>;
  saveCanvasPdf: (canvas: HTMLCanvasElement) => Promise<boolean>;
  printCanvas: (canvas: HTMLCanvasElement) => Promise<boolean>;
  printCaptured: () => Promise<boolean>;
  handlePrintClick: () => void;
  openOriginalPreview: () => void;
}

const PrintContext = React.createContext<PrintContextValue | null>(null);

const usePrintContext = () => {
  const context = React.useContext(PrintContext);
  if (!context) {
    throw new Error("usePrintContext must be used within Print component");
  }
  return context;
};

// Root Component
interface PrintProps extends React.HTMLAttributes<HTMLDivElement> {
  enableCaptureMode?: boolean;
}

const Print = forwardRef<HTMLDivElement, PrintProps>(
  ({ className, children, enableCaptureMode = false, ...props }, ref) => {
    const {
      // 다이얼로그 컨트롤
      dialogs, // { capture, paper, original }

      // 상태
      currentPaperSize,
      isCapturing,
      captureResult,

      getPaperSizeInPixels,

      // 액션
      changePaperSize,
      startArea,
      captureCurrentView,
      startPaperCapture,
      saveCapturedPng,
      saveCanvasPng,
      saveCapturedPdf,
      saveCanvasPdf,
      printCanvas,
      printCaptured,
      handlePrintClick,
      openOriginalPreview,
    } = usePrint({ enableCaptureMode });

    return (
      <PrintContext.Provider
        value={{
          enableCaptureMode,

          getPaperSizeInPixels,

          // 다이얼로그 컨트롤
          dialogs, // { capture, paper, original }
          // 상태
          currentPaperSize,
          isCapturing,
          captureResult,

          // 액션
          changePaperSize,
          startArea,
          captureCurrentView,
          startPaperCapture,
          saveCapturedPng,
          saveCanvasPng,
          saveCapturedPdf,
          saveCanvasPdf,
          printCanvas,
          printCaptured,
          handlePrintClick,
          openOriginalPreview,
        }}
      >
        <div
          ref={ref}
          className={cn(
            "absolute right-4 top-35 flex flex-col gap-2",
            className,
          )}
          {...props}
        >
          <HoverCard>{children}</HoverCard>
        </div>
      </PrintContext.Provider>
    );
  },
);
Print.displayName = "Print";

// Trigger Component
interface PrintTriggerProps
  extends React.ComponentPropsWithoutRef<typeof Button> {}

const PrintTrigger = forwardRef<
  React.ComponentRef<typeof Button>,
  PrintTriggerProps
>(({ className, children, ...props }, ref) => {
  const { handlePrintClick } = usePrintContext();

  return (
    <HoverCardTrigger asChild>
      <Button
        ref={ref}
        className={cn(
          "cursor-pointer bg-white text-gray-600 hover:bg-white hover:text-black opacity-80",
          className,
        )}
        onClick={handlePrintClick}
        {...props}
      >
        {children}
      </Button>
    </HoverCardTrigger>
  );
});
PrintTrigger.displayName = "PrintTrigger";

interface PrintCaptureDialogProps
  extends React.ComponentPropsWithoutRef<typeof Dialog> {}

const PrintCaptureDialog = ({
  children,
  ...props
}: PrintCaptureDialogProps) => {
  const {
    dialogs: { capture },
  } = usePrintContext();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      capture.closeDialog();
    } else {
      capture.openDialog();
    }
  };

  return (
    <Dialog open={capture.open} onOpenChange={handleOpenChange} {...props}>
      {children}
    </Dialog>
  );
};

// Content Component
interface PrintCaptureContentProps
  extends React.ComponentPropsWithoutRef<typeof DialogContent> {}

const PrintCaptureContent = forwardRef<
  HTMLDivElement,
  PrintCaptureContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <DialogContent
      ref={ref}
      className={cn("max-w-md z-[99999999]", className)}
      {...props}
    >
      {children}
    </DialogContent>
  );
});
PrintCaptureContent.displayName = "PrintCaptureContent";

// Header Component
interface PrintCaptureHeaderProps
  extends React.ComponentPropsWithoutRef<typeof DialogHeader> {
  title?: string;
  description?: string;
}

const PrintCaptureHeader = forwardRef<HTMLDivElement, PrintCaptureHeaderProps>(
  (
    {
      className,
      title = "인쇄 및 캡처",
      children,
      description = "인쇄 및 캡처 옵션을 선택하세요.",
      ...props
    },
    ref,
  ) => {
    return (
      <DialogHeader ref={ref} className={className} {...props}>
        <DialogTitle className="flex items-center gap-2">
          <Printer className="w-5 h-5" />
          {title}
        </DialogTitle>
        {children}
        <DialogDescription>{description}</DialogDescription>
      </DialogHeader>
    );
  },
);
PrintCaptureHeader.displayName = "PrintCaptureHeader";

// Option Button Component
interface PrintCaptureOptionButtonProps
  extends React.ComponentPropsWithoutRef<typeof Button> {
  captureType: CaptureType;
  icon?: React.ReactNode;
  label?: string;
  onCapture?: () => void;
}

const PrintCaptureOptionButton = forwardRef<
  React.ComponentRef<typeof Button>,
  PrintCaptureOptionButtonProps
>(
  (
    { className, captureType, icon, label, onCapture, children, ...props },
    ref,
  ) => {
    const { startArea, captureCurrentView, startPaperCapture } =
      usePrintContext();

    const defaultIcons = {
      area: <Square className="w-6 h-6" />,
      "current-view": <Camera className="w-6 h-6" />,
      paper: <StickyNote className="w-6 h-6" />,
    };

    const defaultLabels = {
      area: "영역 캡처",
      "current-view": "화면 캡처",
      paper: "용지별 캡처",
    };

    const defaultHandlers = {
      area: startArea,
      "current-view": captureCurrentView,
      paper: startPaperCapture,
    };

    return (
      <Button
        ref={ref}
        variant="outline"
        className={cn("h-20 flex flex-col items-center gap-2", className)}
        onClick={onCapture || defaultHandlers[captureType]}
        {...props}
      >
        {icon || defaultIcons[captureType]}
        <span className="text-sm">{label || defaultLabels[captureType]}</span>
        {children}
      </Button>
    );
  },
);
PrintCaptureOptionButton.displayName = "PrintCaptureOptionButton";

// Options Component
interface PrintCaptureOptionsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  onAreaCapture?: () => void;
  onCurrentViewCapture?: () => void;
  onPaperCapture?: () => void;
}

const PrintCaptureOptions = forwardRef<
  HTMLDivElement,
  PrintCaptureOptionsProps
>(
  (
    {
      className,
      onAreaCapture,
      onCurrentViewCapture,
      onPaperCapture,
      children,
      ...props
    },
    ref,
  ) => {
    return (
      <div
        ref={ref}
        className={cn("grid grid-cols-3 gap-3", className)}
        {...props}
      >
        <PrintCaptureOptionButton
          captureType="area"
          onCapture={onAreaCapture}
        />
        <PrintCaptureOptionButton
          captureType="current-view"
          onCapture={onCurrentViewCapture}
        />
        <PrintCaptureOptionButton
          captureType="paper"
          onCapture={onPaperCapture}
        />
        {children}
      </div>
    );
  },
);
PrintCaptureOptions.displayName = "PrintCaptureOptions";

// Preview Component
interface PrintCapturePreviewProps
  extends React.HTMLAttributes<HTMLDivElement> {
  preview?: React.ReactNode;
  placeholder?: string;
}

const PrintCapturePreview = forwardRef<
  HTMLDivElement,
  PrintCapturePreviewProps
>(
  (
    {
      className,
      preview,
      placeholder = "캡처할 영역을 선택하세요",
      children,
      ...props
    },
    ref,
  ) => {
    const { captureResult } = usePrintContext();

    const renderPreview = () => {
      if (preview) return preview;

      if (captureResult) {
        return (
          <img
            src={captureResult.canvas.toDataURL()}
            alt="캡처된 이미지"
            className="w-full h-auto max-h-48 object-contain rounded"
          />
        );
      }

      return <p className="text-center text-gray-500 text-sm">{placeholder}</p>;
    };

    return (
      <div
        ref={ref}
        className={cn(
          "border rounded-lg p-4 bg-gray-50 min-h-32 flex items-center justify-center",
          className,
        )}
        {...props}
      >
        {renderPreview()}
        {children}
      </div>
    );
  },
);
PrintCapturePreview.displayName = "PrintCapturePreview";

// Actions Component
interface PrintCaptureActionsProps
  extends React.HTMLAttributes<HTMLDivElement> {
  onSavePng?: () => void;
  onSavePdf?: () => void;
  onPrint?: () => void;
  onViewOriginal?: () => void;
  showSaveOptions?: boolean;
  showPrintButton?: boolean;
  showViewOriginalButton?: boolean;
}

const PrintCaptureActions = forwardRef<
  HTMLDivElement,
  PrintCaptureActionsProps
>(
  (
    {
      className,
      onSavePng,
      onSavePdf,
      onPrint,
      onViewOriginal,
      showSaveOptions = true,
      showPrintButton = true,
      showViewOriginalButton = true,
      children,
      ...props
    },
    ref,
  ) => {
    const {
      saveCapturedPng,
      saveCapturedPdf,
      printCaptured,
      captureResult,
      openOriginalPreview,
    } = usePrintContext();

    return (
      <div ref={ref} className={cn("space-y-3", className)} {...props}>
        {showViewOriginalButton && captureResult && (
          <Button
            className="w-full"
            variant="outline"
            onClick={onViewOriginal ?? openOriginalPreview}
          >
            <Eye className="w-4 h-4 mr-2" />
            원본크기로 보기
          </Button>
        )}

        {showSaveOptions && (
          <div className="flex gap-2">
            <Button
              className="flex-1"
              variant="outline"
              onClick={onSavePng || saveCapturedPng}
              disabled={!captureResult}
            >
              <Download className="w-4 h-4 mr-2" />
              PNG 저장
            </Button>
            <Button
              className="flex-1"
              variant="outline"
              onClick={onSavePdf || saveCapturedPdf}
              disabled={!captureResult}
            >
              <Download className="w-4 h-4 mr-2" />
              PDF 저장
            </Button>
          </div>
        )}

        {showPrintButton && (
          <Button
            className="w-full"
            onClick={onPrint || printCaptured}
            disabled={!captureResult}
          >
            <Printer className="w-4 h-4 mr-2" />
            인쇄하기
          </Button>
        )}

        {children}
      </div>
    );
  },
);
PrintCaptureActions.displayName = "PrintCaptureActions";

const PrintCaptureOriginalDialog = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>((props, ref) => {
  const {
    captureResult,
    dialogs: { original },
  } = usePrintContext();

  if (!captureResult) return null;

  const { canvas } = captureResult;
  const imageWidth = canvas.width;
  const imageHeight = canvas.height;

  // 최대 크기 제한만 설정
  const maxDialogWidth =
    typeof window !== "undefined"
      ? Math.min(window.innerWidth * 0.9, 1200)
      : 1200;
  const maxDialogHeight =
    typeof window !== "undefined"
      ? Math.min(window.innerHeight * 0.9, 800)
      : 800;

  return (
    <Dialog open={original.open} onOpenChange={original.onOpenChange}>
      <DialogContent
        className="z-[99999999] p-0 flex flex-col w-auto h-auto overflow-hidden"
        style={{
          maxWidth: maxDialogWidth,
          maxHeight: maxDialogHeight,
        }}
        ref={ref}
        {...props}
      >
        {/* 고정 헤더 */}
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b">
          <DialogTitle className="flex items-center justify-between">
            <span>캡처 이미지 - 원본크기</span>
            <div className="flex flex-col items-end text-sm font-normal text-gray-500">
              <span>
                {imageWidth} × {imageHeight}px
              </span>
              <span className="text-xs text-blue-600">100% 원본크기</span>
            </div>
          </DialogTitle>
          <DialogDescription>
            캡처된 이미지를 원본크기로 확인할 수 있습니다.
          </DialogDescription>
        </DialogHeader>

        {/* 🎯 스크롤 가능한 이미지 영역 - flex 제거하고 직접 크기 설정 */}
        <div
          className="flex-1 overflow-auto p-6"
          style={{ minWidth: 0, minHeight: 0 }}
        >
          <div
            className="flex items-center justify-center"
            style={{
              minWidth: imageWidth + 48, // 패딩 포함한 최소 너비
              minHeight: imageHeight + 48, // 패딩 포함한 최소 높이
            }}
          >
            <div className="border rounded bg-white shadow-sm">
              <img
                src={canvas.toDataURL()}
                alt="캡처된 이미지 원본"
                className="block rounded"
                style={{
                  width: imageWidth,
                  height: imageHeight,
                }}
              />
            </div>
          </div>
        </div>

        {/* 고정 푸터 */}
        <div className="px-6 pb-4 flex-shrink-0 border-t bg-gray-50">
          <p className="text-xs text-gray-600 text-center py-2">
            이미지를 원본 크기로 표시합니다. 스크롤하여 전체를 확인하세요.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
});
PrintCaptureOriginalDialog.displayName = "PrintCaptureOriginalDialog";

const PrintPaperDialog = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>((props, ref) => {
  const {
    dialogs: { paper },
    getPaperSizeInPixels,
    currentPaperSize,
    saveCanvasPng,
    saveCanvasPdf,
    printCanvas,
  } = usePrintContext();
  // TODO: A0(최희라) 테스트용 코드. 맵 컴포넌트 리팩토링 완료 및 a0 프린트 인터페이스 확정 후 수정 필요
  const { odf } = useMap();
  const [map, setMap] = useState<any | null>(null);
  //const [mapSize, setMapSize] = useState<any | null>({ width: 0, height: 0 });
  useEffect(() => {
    if (map) {
      const pageDpi = {
        A0: 110,
        A1: 150,
        A2: 180,
        A3: 200,
        A4: 240,
        "A0-L": 110,
        "A1-L": 150,
        "A2-L": 180,
        "A3-L": 200,
        "A4-L": 240,
      };
      const paperPixels = getPaperSizeInPixels(
        currentPaperSize,
        pageDpi[currentPaperSize],
        false,
        0,
      );

      //setMapSize({ width: paperPixels.width, height: paperPixels.height });

      // 1. DOM 컨테이너 크기 설정
      const mapContainer = map.getTarget();
      if (mapContainer) {
        mapContainer.style.width = `${paperPixels.width}px`;
        mapContainer.style.height = `${paperPixels.height}px`;
      }
      // 2. 지도 크기 업데이트
      map.updateSize();
    }
  }, [currentPaperSize, getPaperSizeInPixels, map]);
  useEffect(() => {
    // Dialog가 열려있고 ODF가 준비되었을 때만 실행
    if (!paper.open || !odf) return;
    // 임시소스
    setTimeout(() => {
      const mapContainer = document.getElementById("print-widget-map");
      const coord = new odf.Coordinate(199312.9996, 551784.6924);
      const mapOption = {
        center: coord,
        zoom: 11,
        projection: "EPSG:5186",
        baroEMapURL: "https://geon-gateway.geon.kr/map/api/map/baroemap",
        baroEMapAirURL: "https://geon-gateway.geon.kr/map/api/map/ngisair",
        basemap: {
          baroEMap: ["eMapAIR", "eMapColor", "eMapWhite"],
        },
        pixelRatio: 1,
        optimization: true,
      };
      const map = new odf.Map(mapContainer, mapOption);
      setMap(map);
    }, 1000); // 100ms 지연
  }, [odf, paper.open]);

  const printMap = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await printCanvas(mapCanvas);
      }
    }
  };

  const downloadPng = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPng(mapCanvas);
      }
    }
  };
  const downloadPdf = async () => {
    if (map) {
      const mapCanvas = document.querySelector(
        "#print-widget-map canvas",
      ) as HTMLCanvasElement;
      if (mapCanvas) {
        await saveCanvasPdf(mapCanvas);
      }
    }
  };

  return (
    <Dialog open={paper.open} onOpenChange={paper.onOpenChange}>
      <DialogContent
        className="z-[99999999] p-0 flex flex-col"
        ref={ref}
        style={{
          // position: "fixed",
          width: "min(95vw, 1000px)",
          height: "min(95vh, 800px)",
          maxWidth: "none",
          maxHeight: "none",
        }}
      >
        {/* 고정 헤더 */}
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b">
          <DialogTitle className="flex items-center justify-between">
            <span>용지별 캡쳐(테스트용)</span>
          </DialogTitle>
          <DialogDescription>
            용지를 선택하고, 지도를 이동해보세요.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto p-6">
          <div className="space-y-4">
            {/* 용지 선택 */}
            <div className="flex items-center justify-center gap-3">
              <PaperSizeSelect />
              <div className="flex space-x-2">
                <Button
                  onClick={printMap}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Printer className="h-4 w-4" />
                  인쇄
                </Button>

                <Button
                  onClick={downloadPng}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Download className="h-4 w-4" />
                  PNG 저장
                </Button>

                <Button
                  onClick={downloadPdf}
                  variant="outline"
                  className="flex items-center gap-2 rounded-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
                >
                  <Download className="h-4 w-4" />
                  PDF 저장
                </Button>
              </div>
            </div>
            {/* 🎯 지도 미리보기 */}
            <div className="w-auto border rounded">
              {/* 임시 소스 지도 개편된후 수정예정 */}
              <div
                id="print-widget-map"
                className="max-h-screen h-screen max-w-screen w-screen"
              ></div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
});
PrintPaperDialog.displayName = "PrintPaperDialog";

interface PaperSizeSelectProps
  extends Omit<React.ComponentPropsWithoutRef<typeof Select>, "children"> {
  className?: string;
  style?: React.CSSProperties;
}

const PaperSizeSelect = React.forwardRef<
  HTMLButtonElement,
  PaperSizeSelectProps
>(({ className, style, ...props }, ref) => {
  const { currentPaperSize, changePaperSize } = usePrintContext();
  return (
    <Select value={currentPaperSize} onValueChange={changePaperSize}>
      <SelectTrigger
        ref={ref}
        className={cn("", className)}
        style={style}
        {...props}
      >
        <SelectValue placeholder="용지를 선택해주세요." />
      </SelectTrigger>
      <SelectContent className="z-[99999999]">
        {Object.entries(PAPER_SIZE_LABELS).map(([key, label]) => (
          <SelectItem key={key} value={key}>
            {label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});
PaperSizeSelect.displayName = "PaperSizeSelect";

interface PrintWidgetProps extends PrintProps {}

const PrintWidget = forwardRef<HTMLDivElement, PrintWidgetProps>(
  (props, ref) => {
    return (
      <Print ref={ref} {...props}>
        <PrintTrigger>
          <Printer className="w-5 h-5" />
        </PrintTrigger>

        <PrintCaptureDialog>
          <PrintCaptureContent>
            <PrintCaptureHeader />
            <div className="space-y-4">
              <PrintCaptureOptions />
              <PrintCapturePreview />
              <PrintCaptureActions />
            </div>
          </PrintCaptureContent>
        </PrintCaptureDialog>

        <PrintCaptureOriginalDialog />
        <PrintPaperDialog />
      </Print>
    );
  },
);
PrintWidget.displayName = "PrintWidget";

export {
  type CaptureType,
  Print,
  PrintCaptureActions,
  type PrintCaptureActionsProps,
  PrintCaptureContent,
  type PrintCaptureContentProps,
  PrintCaptureDialog,
  type PrintCaptureDialogProps,
  PrintCaptureHeader,
  type PrintCaptureHeaderProps,
  PrintCaptureOptionButton,
  type PrintCaptureOptionButtonProps,
  PrintCaptureOptions,
  type PrintCaptureOptionsProps,
  PrintCaptureOriginalDialog,
  PrintCapturePreview,
  type PrintCapturePreviewProps,
  type PrintProps,
  PrintTrigger,
  type PrintTriggerProps,
  type PrintType,
  PrintWidget,
  type PrintWidgetProps,
  usePrintContext,
};
