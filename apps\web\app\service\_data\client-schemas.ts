import type {
  ClientFieldSchema,
  ClientSearchSchema,
  MenuClientSchemas,
} from "../../../components/dynamic-search/types";

/**
 * 공통으로 사용되는 검색 필드들 정의
 */
const COMMON_SEARCH_FIELDS: ClientFieldSchema[] = [
  {
    id: "spatialSearch",
    label: "공간 검색",
    type: "spatialSearch",
    order: 1000, // 가장 마지막에 배치
    fullWidth: true,
  },
];

/**
 * 메뉴별 클라이언트 전용 스키마 정의
 */
export const CLIENT_SCHEMAS: MenuClientSchemas = {
  // 도로정보 관리
  road: {
    common: COMMON_SEARCH_FIELDS,
    custom: [
      {
        id: "adminDistrict",
        label: "행정구역",
        type: "select",
        order: 10,
        apiEndpoint: "/api/admin-districts",
        placeholder: "행정구역 선택",
      },
      {
        id: "roadGrade",
        label: "도로등급",
        type: "select",
        order: 20,
        options: [
          { label: "고속도로", value: "highway" },
          { label: "국도", value: "national" },
          { label: "지방도", value: "local" },
          { label: "시군도", value: "city" },
        ],
      },
    ],
    // 기본으로 표시할 필드들
    defaultFields: ["divi", "adminDistrict", "spatialSearch"],
    // 추가 가능한 필드들 (API 스키마의 모든 필드 + 커스텀 필드) API 필드들은 런타임에 동적으로 추가됨
    // availableFields: ["gid", "divi", "updtDt", "roadGrade"]
  },
};

/**
 * 특정 메뉴의 클라이언트 스키마를 가져오는 함수
 */
export function getClientSchema(menuId: string): ClientSearchSchema {
  const schema = CLIENT_SCHEMAS[menuId];
  if (!schema) {
    // 기본 스키마 반환 (공통 필드만 포함)
    return {
      common: COMMON_SEARCH_FIELDS,
      custom: [],
      defaultFields: ["name", "spatialSearch"], // 최소한의 기본 필드
      availableFields: [], // 추가 필드 없음
    };
  }
  return schema;
}
