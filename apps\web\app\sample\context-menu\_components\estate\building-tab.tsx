"use client";

import type { EstateClient } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import { TabsContent } from "@geon-ui/react/primitives/tabs";
import { Search } from "lucide-react";
import { useTranslations } from "next-intl";
import React from "react";
import { useForm } from "react-hook-form";

import BuildingHoTable from "./tables/building-ho";

export default function BuildingTab({
  pnu,
  crtfckey,
  client,
}: {
  pnu: string;
  crtfckey: string;
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.building.ho");
  // Parameter States
  const [params, setParams] = React.useState<{
    buldDongNm?: string;
    buldFloorNm?: string;
    buldHoNm?: string;
  }>({
    buldDongNm: "",
    buldFloorNm: "",
    buldHoNm: "",
  });

  const form = useForm({
    defaultValues: params,
  });

  return (
    <TabsContent
      value="building"
      className="flex h-[500px] w-full flex-col gap-2 overflow-hidden"
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => setParams(data))}
          className="bg-background sticky top-0 z-10 flex justify-between px-2"
        >
          <div className="flex gap-2">
            <FormField
              control={form.control}
              name="buldDongNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(field.name)}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="buldFloorNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(field.name)}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="buldHoNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t(field.name)}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Button type="submit" size="icon" className="self-end">
            <Search />
          </Button>
        </form>
      </Form>
      <BuildingHoTable
        pnu={pnu}
        crtfckey={crtfckey}
        numOfRows={10}
        pageNo={1}
        client={client}
        buldDongNm={params.buldDongNm}
        buldFloorNm={params.buldFloorNm}
        buldHoNm={params.buldHoNm}
      />
    </TabsContent>
  );
}
