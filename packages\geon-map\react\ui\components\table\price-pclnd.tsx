"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function PricePclndTable({
  client,
  ...props
}: APIRequestType<EstateClient["price"]["pclnd"]> & {
  client: EstateClient;
}) {
  // Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["price"]["pclnd"]>
  >({
    queryKey: ["price/pclnd", { ...props, numOfRows, pageNo }],
    queryFn: () => client.price.pclnd({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError)
    return (
      <div className="flex justify-center align-middle">
        Error loading parcel data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">법정동</TableHead>
            <TableHead className="font-bold text-center">지번</TableHead>
            <TableHead className="font-bold text-center">특수지구</TableHead>
            <TableHead className="font-bold text-center">
              공시지가(원/m<sup>2</sup>)
            </TableHead>
            <TableHead className="font-bold text-center">공시일자</TableHead>
            <TableHead className="font-bold text-center">표준지여부</TableHead>
            <TableHead className="font-bold text-center">기준연도</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`building-floor-${idx}`}>
                <TableCell className="text-center">{res.ldCodeNm}</TableCell>
                <TableCell className="text-center">{res.mnnmSlno}</TableCell>
                <TableCell className="text-center">
                  {res.regstrSeCodeNm}
                </TableCell>
                <TableCell className="text-center">{res.pblntfPclnd}</TableCell>
                <TableCell className="text-center">{res.pblntfDe}</TableCell>
                <TableCell className="text-center">{res.stdLandAt}</TableCell>
                <TableCell className="text-center">{res.stdrYear}</TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                No data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
