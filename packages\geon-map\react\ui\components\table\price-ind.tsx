"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function PriceIndTable({
  client,
  ...props
}: APIRequestType<EstateClient["price"]["ind"]> & {
  client: EstateClient;
}) {
  // Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["price"]["ind"]>
  >({
    queryKey: ["price/ind", { ...props, numOfRows, pageNo }],
    queryFn: () => client.price.ind({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError)
    return (
      <div className="flex justify-center align-middle">
        Error loading parcel data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">기준연도</TableHead>
            <TableHead className="font-bold text-center">동</TableHead>
            <TableHead className="font-bold text-center">
              주택가격(원)
            </TableHead>
            <TableHead className="font-bold text-center">
              토지대장 면적(m/<sup>2</sup>)
            </TableHead>
            <TableHead className="font-bold text-center">
              산정대지 면적(m/<sup>2</sup>)
            </TableHead>
            <TableHead className="font-bold text-center">
              건물 전체 연면적(m/<sup>2</sup>)
            </TableHead>
            <TableHead className="font-bold text-center">
              건물 산정 연면적(m/<sup>2</sup>)
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`building-floor-${idx}`}>
                <TableCell className="text-center">{res.stdrYear}</TableCell>
                <TableCell className="text-center">{res.dongCode}</TableCell>
                <TableCell className="text-center">{res.housePc}</TableCell>
                <TableCell className="text-center">{res.ladRegstrAr}</TableCell>
                <TableCell className="text-center">{res.calcPlotAr}</TableCell>
                <TableCell className="text-center">
                  {res.buldAllTotAr}
                </TableCell>
                <TableCell className="text-center">
                  {res.buldCalcTotAr}
                </TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={7} className="text-center">
                No data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
