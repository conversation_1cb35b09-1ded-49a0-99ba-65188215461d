"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";

/**
 * BasemapProvider 설정 옵션
 */
export interface BasemapProviderOptions {
  /** Basemap Control 초기화 옵션 */
  basemapOptions?: {
    basemapList?: any;
    urls?: any;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 BasemapProvider (Basemap Control 설정 전용)
 *
 * Basemap Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <BasemapProvider basemapOptions={{ basemapList: [...] }}>
 *       <BasemapSwitcher />
 *     </BasemapProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function BasemapProvider({
  children,
  basemapOptions = {},
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<BasemapProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Basemap 설정 등록
    updateConfig({
      basemapOptions,
      autoInitialize,
      onError,
    });
  }, [basemapOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
