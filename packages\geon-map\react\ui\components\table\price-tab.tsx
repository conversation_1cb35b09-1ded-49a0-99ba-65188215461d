import type { EstateClient } from "@geon-query/model";
import { But<PERSON> } from "@geon-ui/react/primitives/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import { TabsContent } from "@geon-ui/react/primitives/tabs";
import { Search } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";

import PriceAptTable from "./price-apt";
import PriceIndTable from "./price-ind";
import PricePclndTable from "./price-pclnd";

export default function PriceTab({
  pnu,
  crtfckey,
  client,
}: {
  pnu: string;
  crtfckey: string;
  client: EstateClient;
}) {
  // Parameter States
  const [params, setParams] = React.useState<{
    dongNm?: string;
    hoNm?: string;
    stdrYear?: string;
  }>({
    dongNm: "101",
    hoNm: "201",
    stdrYear: "2012",
  });

  const form = useForm({
    defaultValues: params,
  });

  return (
    <TabsContent
      value="price"
      className="flex flex-col gap-2 w-full h-[500px] overflow-hidden overflow-y-auto"
    >
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => setParams(data))}
          className="flex justify-between"
        >
          <div className="flex gap-2">
            <FormField
              control={form.control}
              name="stdrYear"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>기준 연도</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="dongNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>동</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="hoNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>호실</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Button type="submit" size="icon" className="self-end">
            <Search />
          </Button>
        </form>
      </Form>

      <div>
        <span>price/pclnd</span>
        <PricePclndTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          stdrYear={params.stdrYear}
        />
      </div>
      <div>
        <span>price/ind</span>
        <PriceIndTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          stdrYear={params.stdrYear}
        />
      </div>
      <div>
        <span>price/apt</span>
        <PriceAptTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          stdrYear={params.stdrYear}
          dongNm={params.dongNm}
          hoNm={params.hoNm}
        />
      </div>
    </TabsContent>
  );
}
