"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Badge } from "@geon-ui/react/primitives/badge";

import { SpatialSearchStatus } from "@/components/dynamic-search/spatial-search";

import { useFacilitySearch } from "../../_contexts/facility-search";

export function FacilitySearchHeader() {
  const {
    selectedServiceId,
    availableFacilities,
    selectedFacilityIds,
    toggleFacility,
    spatialSearch,
    setSpatialSearch,
  } = useFacilitySearch();

  if (!selectedServiceId || availableFacilities.length === 0) {
    return (
      <div className="flex h-12 items-center justify-center px-4">
        <span className="text-muted-foreground text-sm">로딩 중...</span>
      </div>
    );
  }

  return (
    <div className="w-full space-y-3 p-3">
      {/* 시설물 필터와 공간 검색 상태 */}
      <div className="space-y-2">
        {/* 전체/해제 컨트롤 */}
        <div className="flex items-center gap-2">
          <SpatialSearchStatus
            value={spatialSearch}
            onClear={() => setSpatialSearch(null)}
          />
        </div>

        {/* 시설물 Badge들 - 반응형 */}
        <div className="flex flex-wrap gap-1.5">
          {availableFacilities.map((facility) => {
            const isSelected = selectedFacilityIds.includes(facility.id);
            return (
              <Badge
                key={facility.id}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "h-6 cursor-pointer px-2 text-xs transition-all duration-200",
                  "hover:scale-105 hover:shadow-sm",
                  isSelected
                    ? "border-transparent shadow-sm"
                    : "hover:border-primary/50",
                )}
                style={{
                  backgroundColor: isSelected ? facility.color : undefined,
                  borderColor: !isSelected ? facility.color + "40" : undefined,
                  color: isSelected ? "white" : undefined,
                }}
                onClick={() => toggleFacility(facility.id)}
              >
                {facility.title}
              </Badge>
            );
          })}
        </div>
      </div>
    </div>
  );
}
