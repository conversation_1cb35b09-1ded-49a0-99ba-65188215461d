"use client";

import type { FacilityDetailViewProps } from "../../_types/facility-detail";
import { DocumentView, DocumentSection, DocumentField } from "../common/document-view";

/**
 * 날짜 포맷팅 유틸리티
 */
function formatDate(date: Date | string | undefined): string {
  if (!date) return "-";

  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return "-";

  return dateObj.toLocaleDateString("ko-KR", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

export function FacilityDetailView({ data }: FacilityDetailViewProps) {
  // 기본 정보 필드 구성
  const basicInfoFields: DocumentField[] = [
    {
      label: "대장구분",
      value: data.registryType,
      type: "text"
    },
    {
      label: "관리번호",
      value: data.managementNumber,
      type: "text",
      highlight: true
    },
    {
      label: "행정구역",
      value: data.adminDistrict,
      type: "text"
    },
    {
      label: "도엽번호",
      value: data.mapSheetNumber,
      type: "text"
    },
    {
      label: "관리기관",
      value: data.managementAgency,
      type: "text",
      fullWidth: true
    },
    {
      label: "설치 시작일",
      value: formatDate(data.installStartDate),
      type: "date"
    },
    {
      label: "설치 종료일",
      value: formatDate(data.installEndDate),
      type: "date"
    }
  ];

  // 문서 섹션 구성
  const sections: DocumentSection[] = [
    {
      title: "기본 정보",
      content: basicInfoFields
    }
  ];

  // 영역정보가 있는 경우 섹션 추가
  if (data.areaInfo) {
    sections.push({
      title: "영역정보",
      content: (
        <div className="border-2 border-gray-800 p-4">
          <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800">
            {data.areaInfo}
          </pre>
        </div>
      )
    });
  }

  // 비고가 있는 경우 섹션 추가
  if (data.remarks) {
    sections.push({
      title: "비고",
      content: (
        <div className="border-2 border-gray-800 p-4">
          <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-800">
            {data.remarks}
          </pre>
        </div>
      )
    });
  }

  return (
    <DocumentView
      title="시설물 관리대장"
      documentNumber={data.managementNumber}
      createdDate={formatDate(data.installStartDate)}
      location={data.adminDistrict}
      sections={sections}
      className="max-w-4xl"
    />
  );
}
