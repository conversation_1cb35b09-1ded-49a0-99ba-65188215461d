"use client";

import type { FacilityDetailViewProps } from "../../_types/facility-detail";

/**
 * 날짜 포맷팅 유틸리티
 */
function formatDate(date: Date | string | undefined): string {
  if (!date) return "-";

  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return "-";

  return dateObj.toLocaleDateString("ko-KR", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}

export function FacilityDetailView({ data }: FacilityDetailViewProps) {
  return (
    <div
      className="max-w-4xl bg-white p-2"
      style={{ fontFamily: "Malgun Gothic, sans-serif" }}
    >
      {/* 문서 헤더 */}
      {/* <div className="mb-6 border-b-4 border-gray-800 pb-4">
        <h1 className="mb-2 text-center text-2xl font-bold text-gray-900">
          시설물 관리대장
        </h1>
      </div> */}

      {/* 기본 정보 테이블 */}
      <div className="mb-8">
        <table className="w-full border-collapse border-2 border-gray-800">
          <tbody>
            <tr>
              <td className="w-32 border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                대장구분
              </td>
              <td className="border border-gray-600 px-4 py-3 text-sm">
                {data.registryType || "-"}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                관리번호
              </td>
              <td className="border border-gray-600 px-4 py-3 font-mono text-sm">
                {data.managementNumber || "-"}
              </td>
              <td className="border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                행정구역
              </td>
              <td className="border border-gray-600 px-4 py-3 text-sm">
                {data.adminDistrict || "-"}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                도엽번호
              </td>
              <td className="border border-gray-600 px-4 py-3 font-mono text-sm">
                {data.mapSheetNumber || "-"}
              </td>
              <td className="border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                관리기관
              </td>
              <td className="border border-gray-600 px-4 py-3 text-sm">
                {data.managementAgency || "-"}
              </td>
            </tr>
            <tr>
              <td className="w-32 border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                설치 시작일
              </td>
              <td className="border border-gray-600 px-4 py-3 font-mono text-sm">
                {formatDate(data.installStartDate)}
              </td>
              <td className="w-32 border border-gray-600 bg-gray-100 px-4 py-3 text-sm font-medium">
                설치 종료일
              </td>
              <td className="border border-gray-600 px-4 py-3 font-mono text-sm">
                {formatDate(data.installEndDate)}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* 영역정보 */}
      {data.areaInfo && (
        <div className="mb-8">
          <h2 className="mb-4 border-b border-gray-400 pb-2 text-lg font-bold">
            ■ 영역정보
          </h2>
          <div className="border-2 border-gray-800 p-4">
            <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800">
              {data.areaInfo}
            </pre>
          </div>
        </div>
      )}

      {/* 비고 */}
      {data.remarks && (
        <div className="mb-8">
          <h2 className="mb-4 border-b border-gray-400 pb-2 text-lg font-bold">
            ■ 비고
          </h2>
          <div className="border-2 border-gray-800 p-4">
            <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-800">
              {data.remarks}
            </pre>
          </div>
        </div>
      )}

      {/* 문서 다운로드 섹션 */}
      {/* <div className="mt-12 border-t-2 border-gray-300 pt-6">
        <h2 className="mb-4 border-b border-gray-400 pb-2 text-lg font-bold">
          ■ 문서 출력
        </h2>
        <div className="rounded-sm border border-gray-300 bg-gray-50 p-4">
          <div className="flex flex-wrap gap-3">
            <FacilityDownloadButtons data={data} loading={loading} />
          </div>
        </div>
      </div> */}

      {/* 문서 푸터 */}
      {/* <div className="mt-8 border-t border-gray-400 pt-4 text-center text-xs text-gray-500">
        본 문서는 시설물 관리시스템에서 자동 생성된 문서입니다.
        <br />
        발행일시: {new Date().toLocaleString("ko-KR")}
      </div> */}
    </div>
  );
}
