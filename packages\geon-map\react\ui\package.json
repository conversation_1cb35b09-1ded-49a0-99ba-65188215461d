{"name": "@geon-map/react-ui", "version": "0.0.0", "exports": {"./components": "./components/index.ts", "./types": "./types/index.ts", "./hooks": "./hooks/index.ts", "./utils": "./utils/index.ts"}, "files": ["dist/**"], "scripts": {"build": "tsup", "lint": "eslint . --max-warnings 0", "test": "vitest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@geon-map/core": "workspace:^", "@geon-map/react-odf": "workspace:^", "@geon-query/model": "workspace:^", "@geon-query/react-query": "workspace:*", "@geon-ui/react": "workspace:^", "clsx": "^2.1.1", "es-toolkit": "^1.39.10", "lucide-react": "^0.542.0", "radix-ui": "^1.4.3", "re-resizable": "^6.11.2", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@types/node": "^22.18.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "eslint": "^9.35.0", "react": "^19.1.1", "tsup": "^8.5.0", "typescript": "^5.9.2"}, "peerDependencies": {"react": "^19.0.0"}, "publishConfig": {"access": "restricted", "registry": "https://nexus.geon.kr/repository/npm-private/"}}