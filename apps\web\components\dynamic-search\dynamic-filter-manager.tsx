"use client";

import { Badge } from "@geon-ui/react/primitives/badge";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@geon-ui/react/primitives/dropdown-menu";
import { Check, Filter, X } from "lucide-react";
import { useState } from "react";

import type { FieldType } from "./types";

export type AvailableField = {
  id: string;
  label: string;
  type: FieldType;
};

export type ActiveField = {
  id: string;
  label: string;
  removable: boolean; // 기본 필드는 제거 불가
};

export type DynamicFilterManagerProps = {
  activeFields: ActiveField[];
  availableFields: AvailableField[];
  onAddField: (fieldId: string) => void;
  onRemoveField: (fieldId: string) => void;
  disabled?: boolean;
  compact?: boolean;
};

export function DynamicFilterManager({
  activeFields,
  availableFields,
  onAddField,
  onRemoveField,
  disabled = false,
  compact = true,
}: DynamicFilterManagerProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleAddField = (fieldId: string) => {
    onAddField(fieldId);
    setIsDropdownOpen(false);
  };

  const getFieldTypeIcon = (type: FieldType): string => {
    switch (type) {
      case "text":
        return "📝";
      case "number":
        return "🔢";
      case "select":
        return "📋";
      case "date":
        return "📅";
      case "dateRange":
        return "📆";
      case "buttonGroup":
        return "🔘";
      case "spatialSearch":
        return "🗺️";
      default:
        return "🔍";
    }
  };

  return (
    <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size={compact ? "sm" : "default"}
          className="h-7 w-fit justify-center text-xs"
          disabled={disabled}
        >
          <Filter className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="max-h-60 w-64 overflow-y-auto"
      >
        {/* 현재 활성화된 필드들 */}
        {activeFields.length > 0 && (
          <>
            <div className="text-muted-foreground px-3 py-2 text-xs font-medium">
              활성 검색 필터
            </div>
            {activeFields.map((field) => (
              <DropdownMenuItem
                key={`active-${field.id}`}
                className="cursor-pointer text-xs"
                onClick={
                  field.removable ? () => onRemoveField(field.id) : undefined
                }
              >
                <Check className="mr-2 h-3 w-3 text-green-600" />
                <span className="flex-1">{field.label}</span>
                {field.removable && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveField(field.id);
                    }}
                    disabled={disabled}
                    className="focus:ring-ring ml-1 rounded-sm opacity-60 hover:opacity-100 focus:outline-none focus:ring-1 disabled:opacity-30"
                  >
                    <X className="h-3 w-3" />
                  </button>
                )}
                {!field.removable && (
                  <Badge variant="outline" className="px-1 py-0 text-[10px]">
                    기본
                  </Badge>
                )}
              </DropdownMenuItem>
            ))}
            {availableFields.length > 0 && <DropdownMenuSeparator />}
          </>
        )}

        {/* 추가 가능한 필드들 */}
        {availableFields.length > 0 ? (
          <>
            <div className="text-muted-foreground px-3 py-2 text-xs font-medium">
              추가 가능한 검색 필터
            </div>
            {availableFields.map((field) => (
              <DropdownMenuItem
                key={`available-${field.id}`}
                className="cursor-pointer text-xs"
                onClick={() => handleAddField(field.id)}
              >
                <span className="mr-2">{getFieldTypeIcon(field.type)}</span>
                <span className="flex-1">{field.label}</span>
                <Badge variant="outline" className="px-1 py-0 text-[10px]">
                  {field.type}
                </Badge>
              </DropdownMenuItem>
            ))}
          </>
        ) : (
          activeFields.length === 0 && (
            <div className="text-muted-foreground px-3 py-2 text-center text-xs">
              사용 가능한 필터가 없습니다
            </div>
          )
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
