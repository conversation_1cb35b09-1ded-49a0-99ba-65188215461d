import { Print, PrintControlOptions } from "@geon-map/core";
import { useCallback, useEffect, useRef } from "react";

import { useMapStore } from "../stores/map-store";

export interface UsePrintOptions extends PrintControlOptions {}
export interface UsePrintReturn {
  print: () => void;
}

export const usePrint = (options: UsePrintOptions = {}): UsePrintReturn => {
  const map = useMapStore((s) => s.map);
  const odf = useMapStore((s) => s.odf);

  const printRef = useRef<Print | null>(null);

  useEffect(() => {
    if (!map || !odf) {
      printRef.current = null;
      return;
    }

    const printControl = new Print(odf, options);
    printControl.setMap(map);
    printRef.current = printControl;

    return () => {
      printControl.removeMap();
      printRef.current = null;
    };
  }, [map, odf, options]);

  const print = useCallback(() => {
    printRef.current?.print();
  }, []);

  return { print };
};
