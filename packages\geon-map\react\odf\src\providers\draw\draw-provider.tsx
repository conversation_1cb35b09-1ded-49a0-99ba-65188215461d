"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";
import type { DrawControlOptions } from "../../types/draw-types";

/**
 * DrawProvider 설정 옵션
 */
export interface DrawProviderOptions {
  /** Draw Control 초기화 옵션 */
  drawOptions?: DrawControlOptions;
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 DrawProvider (Draw Control 설정 전용)
 *
 * Draw Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <DrawProvider drawOptions={{ tools: ["polygon", "point"] }}>
 *       <DrawingPanel />
 *     </DrawProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function DrawProvider({
  children,
  drawOptions = {
    continuity: false,
    createNewLayer: false,
    tools: ["text", "polygon", "lineString", "box", "point", "circle", "curve"],
  },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<DrawProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Draw 설정 등록
    updateConfig({
      drawOptions,
      autoInitialize,
      onError,
    });
  }, [drawOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}

// Provider 상태 노출 훅 (Config 기반)
export function useDrawProviderStatus() {
  const { drawOptions } = useControlsConfig();
  return { isProviderSet: !!drawOptions };
}
