"use client";

import type { EstateClient } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import { TabsContent } from "@geon-ui/react/primitives/tabs";
import { Search } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";

import BuildingFloorTable from "./building-floor";
import BuildingHoTable from "./building-ho";
import BuildingRegisterTable from "./building-register";
import BuildingSerialTable from "./building-serial";

export default function BuildingTab({
  pnu,
  crtfckey,
  client,
}: {
  pnu: string;
  crtfckey: string;
  client: EstateClient;
}) {
  // Parameter States
  const [params, setParams] = React.useState<{
    agbldgSn?: string;
    buldDongNm?: string;
    buldFloorNm?: string;
    buldHoNm?: string;
  }>({
    agbldgSn: "0001",
    buldDongNm: "1",
    buldFloorNm: "1",
    buldHoNm: "112",
  });

  const form = useForm({
    defaultValues: params,
  });

  return (
    <TabsContent
      value="building"
      className="flex flex-col gap-2 w-full h-[500px] overflow-hidden overflow-y-auto"
    >
      <div>
        <span>build/register</span>
        <BuildingRegisterTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
        />
      </div>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => setParams(data))}
          className="flex justify-between"
        >
          <div className="flex gap-2">
            <FormField
              control={form.control}
              name="agbldgSn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>대지권 일련번호</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="buldDongNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>동</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="buldFloorNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>층</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="buldHoNm"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>호</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Button type="submit" size="icon" className="self-end">
            <Search />
          </Button>
        </form>
      </Form>
      <div>
        <span>build/serial</span>
        <BuildingSerialTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          agbldgSn={params.agbldgSn}
        />
      </div>
      <div>
        <span>build/floor</span>
        <BuildingFloorTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          agbldgSn={params.agbldgSn}
          buldDongNm={params.buldDongNm}
          buldFloorNm={params.buldFloorNm}
        />
      </div>
      <div>
        <span>build/ho</span>
        <BuildingHoTable
          pnu={pnu}
          crtfckey={crtfckey}
          numOfRows={10}
          pageNo={1}
          client={client}
          agbldgSn={params.agbldgSn}
          buldDongNm={params.buldDongNm}
          buldFloorNm={params.buldFloorNm}
          buldHoNm={params.buldHoNm}
        />
      </div>
    </TabsContent>
  );
}
