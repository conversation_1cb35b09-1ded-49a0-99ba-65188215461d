"use client";

import { createEstateClient, crtfckey } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@geon-ui/react/primitives/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@geon-ui/react/primitives/tabs";
import { useTranslations } from "next-intl";
import React from "react";

import BuildingTab from "./building-tab";
import LandUsePlan from "./tables/land-useplan";

interface EstateDialogProps {
  pnu: string;
  tabValue: string;
  setTabValue: (value: string) => void;
  juso?: string;
}

export default function EstateDialogContent({
  pnu,
  tabValue,
  setTabValue,
  juso,
}: EstateDialogProps) {
  // message handler
  const t = useTranslations("dialog");
  const client = createEstateClient();

  return (
    <DialogContent className="max-h-[670px] w-full !max-w-[1000px]">
      <DialogHeader>
        <DialogTitle>통합 행정 정보 조회</DialogTitle>
        <DialogDescription>{juso || "Failed to get address"}</DialogDescription>
      </DialogHeader>
      <Tabs
        value={tabValue}
        className="h-[500px] w-full overflow-hidden overflow-y-auto"
      >
        <TabsList>
          <TabsTrigger value="land" onClick={() => setTabValue("land")}>
            토지
          </TabsTrigger>
          <TabsTrigger value="building" onClick={() => setTabValue("building")}>
            건물
          </TabsTrigger>
          <TabsTrigger value="price" onClick={() => setTabValue("price")}>
            개별 주택 가격
          </TabsTrigger>
          <TabsTrigger
            value="land-useplan"
            onClick={() => setTabValue("land-useplan")}
          >
            토지 이용 계획
          </TabsTrigger>
        </TabsList>

        <TabsContent
          value="land"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        ></TabsContent>
        <TabsContent
          value="building"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <BuildingTab pnu={pnu} crtfckey={crtfckey} client={client} />
        </TabsContent>
        <TabsContent
          value="land-useplan"
          className="flex h-[500px] w-full flex-col gap-2 overflow-hidden overflow-y-auto"
        >
          <LandUsePlan
            pnu={pnu}
            crtfckey={crtfckey}
            pageNo={1}
            numOfRows={10}
            client={client}
          />
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="secondary">
            {t("close")}
          </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  );
}
