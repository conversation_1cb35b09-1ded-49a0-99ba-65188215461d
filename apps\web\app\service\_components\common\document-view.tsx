"use client";

// Badge와 Separator가 없는 경우 간단한 대체 구현
const Badge = ({ children, variant = "default" }: { children: React.ReactNode; variant?: string }) => {
  const variantClasses = {
    default: "bg-blue-100 text-blue-800",
    secondary: "bg-gray-100 text-gray-800",
    destructive: "bg-red-100 text-red-800",
    outline: "border border-gray-300 text-gray-700"
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant as keyof typeof variantClasses] || variantClasses.default}`}>
      {children}
    </span>
  );
};

const Separator = () => <hr className="border-gray-200" />;
import { Calendar, MapPin, User, FileText, Hash } from "lucide-react";
import { ReactNode } from "react";

/**
 * 공문서 스타일 정보 표시 컴포넌트의 Props
 */
export interface DocumentViewProps {
  /** 문서 제목 */
  title: string;
  /** 문서 부제목 (선택사항) */
  subtitle?: string;
  /** 문서 번호 또는 ID */
  documentNumber?: string;
  /** 작성일 */
  createdDate?: string;
  /** 작성자 */
  author?: string;
  /** 위치 정보 */
  location?: string;
  /** 상태 배지 */
  status?: {
    label: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  /** 메인 콘텐츠 섹션들 */
  sections: DocumentSection[];
  /** 추가 CSS 클래스 */
  className?: string;
}

/**
 * 문서 섹션 정의
 */
export interface DocumentSection {
  /** 섹션 제목 */
  title: string;
  /** 섹션 내용 */
  content: DocumentField[] | ReactNode;
  /** 섹션이 접을 수 있는지 여부 */
  collapsible?: boolean;
  /** 기본 접힘 상태 */
  defaultCollapsed?: boolean;
}

/**
 * 문서 필드 정의
 */
export interface DocumentField {
  /** 필드 라벨 */
  label: string;
  /** 필드 값 */
  value: ReactNode;
  /** 필드 타입 (표시 스타일 결정) */
  type?: "text" | "number" | "date" | "location" | "status" | "multiline";
  /** 전체 너비 사용 여부 */
  fullWidth?: boolean;
  /** 강조 표시 여부 */
  highlight?: boolean;
}

/**
 * 공문서 스타일 정보 표시 컴포넌트
 */
export function DocumentView({
  title,
  subtitle,
  documentNumber,
  createdDate,
  author,
  location,
  status,
  sections,
  className = "",
}: DocumentViewProps) {
  return (
    <div className={`bg-white p-6 ${className}`}>
      {/* 문서 헤더 */}
      <div className="mb-6 border-b pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
            {subtitle && (
              <p className="text-lg text-gray-600 mb-3">{subtitle}</p>
            )}
            
            {/* 문서 메타 정보 */}
            <div className="flex flex-wrap gap-4 text-sm text-gray-500">
              {documentNumber && (
                <div className="flex items-center gap-1">
                  <Hash className="h-4 w-4" />
                  <span>{documentNumber}</span>
                </div>
              )}
              {createdDate && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{createdDate}</span>
                </div>
              )}
              {author && (
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>{author}</span>
                </div>
              )}
              {location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{location}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* 상태 배지 */}
          {status && (
            <Badge variant={status.variant || "default"}>
              {status.label}
            </Badge>
          )}
        </div>
      </div>

      {/* 문서 섹션들 */}
      <div className="space-y-6">
        {sections.map((section, index) => (
          <DocumentSectionComponent
            key={index}
            section={section}
          />
        ))}
      </div>
    </div>
  );
}

/**
 * 문서 섹션 컴포넌트
 */
function DocumentSectionComponent({ section }: { section: DocumentSection }) {
  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <FileText className="h-5 w-5 text-gray-600" />
        <h2 className="text-lg font-semibold text-gray-800">{section.title}</h2>
      </div>
      
      <Separator />
      
      <div className="pl-7">
        {Array.isArray(section.content) ? (
          <DocumentFieldsGrid fields={section.content} />
        ) : (
          <div>{section.content}</div>
        )}
      </div>
    </div>
  );
}

/**
 * 문서 필드 그리드 컴포넌트
 */
function DocumentFieldsGrid({ fields }: { fields: DocumentField[] }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {fields.map((field, index) => (
        <DocumentFieldComponent
          key={index}
          field={field}
        />
      ))}
    </div>
  );
}

/**
 * 문서 필드 컴포넌트
 */
function DocumentFieldComponent({ field }: { field: DocumentField }) {
  const getFieldIcon = (type: string) => {
    switch (type) {
      case "date":
        return <Calendar className="h-4 w-4 text-gray-400" />;
      case "location":
        return <MapPin className="h-4 w-4 text-gray-400" />;
      default:
        return null;
    }
  };

  const fieldClasses = `
    ${field.fullWidth ? "md:col-span-2" : ""}
    ${field.highlight ? "bg-blue-50 border border-blue-200 rounded-lg p-3" : ""}
  `;

  return (
    <div className={fieldClasses}>
      <div className="flex items-start gap-2">
        {getFieldIcon(field.type || "text")}
        <div className="flex-1 min-w-0">
          <dt className="text-sm font-medium text-gray-600 mb-1">
            {field.label}
          </dt>
          <dd className={`text-sm ${field.type === "multiline" ? "whitespace-pre-wrap" : ""} text-gray-900`}>
            {field.value || "-"}
          </dd>
        </div>
      </div>
    </div>
  );
}

export default DocumentView;
