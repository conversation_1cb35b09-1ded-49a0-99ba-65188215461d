"use client";

// Badge와 Separator가 없는 경우 간단한 대체 구현
const Badge = ({ children, variant = "default" }: { children: React.ReactNode; variant?: string }) => {
  const variantClasses = {
    default: "bg-blue-100 text-blue-800",
    secondary: "bg-gray-100 text-gray-800",
    destructive: "bg-red-100 text-red-800",
    outline: "border border-gray-300 text-gray-700"
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant as keyof typeof variantClasses] || variantClasses.default}`}>
      {children}
    </span>
  );
};


import { Calendar, MapPin, FileText } from "lucide-react";
import { ReactNode } from "react";

/**
 * 공문서 스타일 정보 표시 컴포넌트의 Props
 */
export interface DocumentViewProps {
  /** 문서 제목 */
  title: string;
  /** 문서 부제목 (선택사항) */
  subtitle?: string;
  /** 문서 번호 또는 ID */
  documentNumber?: string;
  /** 작성일 */
  createdDate?: string;
  /** 작성자 */
  author?: string;
  /** 위치 정보 */
  location?: string;
  /** 상태 배지 */
  status?: {
    label: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  /** 메인 콘텐츠 섹션들 */
  sections: DocumentSection[];
  /** 추가 CSS 클래스 */
  className?: string;
}

/**
 * 문서 섹션 정의
 */
export interface DocumentSection {
  /** 섹션 제목 */
  title: string;
  /** 섹션 내용 */
  content: DocumentField[] | ReactNode;
  /** 섹션이 접을 수 있는지 여부 */
  collapsible?: boolean;
  /** 기본 접힘 상태 */
  defaultCollapsed?: boolean;
}

/**
 * 문서 필드 정의
 */
export interface DocumentField {
  /** 필드 라벨 */
  label: string;
  /** 필드 값 */
  value: ReactNode;
  /** 필드 타입 (표시 스타일 결정) */
  type?: "text" | "number" | "date" | "location" | "status" | "multiline";
  /** 전체 너비 사용 여부 */
  fullWidth?: boolean;
  /** 강조 표시 여부 */
  highlight?: boolean;
}

/**
 * 공문서 스타일 정보 표시 컴포넌트
 */
export function DocumentView({
  title,
  subtitle,
  documentNumber,
  createdDate,
  author,
  location,
  status,
  sections,
  className = "",
}: DocumentViewProps) {
  return (
    <div className={`bg-white ${className}`} style={{ fontFamily: "Malgun Gothic, sans-serif" }}>
      {/* 공문서 스타일 헤더 */}
      <div className="border-b-4 border-gray-800 pb-6 mb-8">
        {/* 문서 제목 */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 tracking-wide">
            {title}
          </h1>
          {subtitle && (
            <div className="text-xl text-gray-700 font-medium border-b border-gray-300 pb-2 inline-block">
              {subtitle}
            </div>
          )}
        </div>

        {/* 문서 정보 테이블 */}
        <div className="max-w-2xl mx-auto">
          <table className="w-full border-collapse border-2 border-gray-800">
            <tbody>
              {documentNumber && (
                <tr>
                  <td className="w-32 border border-gray-600 bg-gray-100 px-4 py-2 text-sm font-bold text-center">
                    문서번호
                  </td>
                  <td className="border border-gray-600 px-4 py-2 text-sm font-mono">
                    {documentNumber}
                  </td>
                </tr>
              )}
              {createdDate && (
                <tr>
                  <td className="border border-gray-600 bg-gray-100 px-4 py-2 text-sm font-bold text-center">
                    작성일자
                  </td>
                  <td className="border border-gray-600 px-4 py-2 text-sm">
                    {createdDate}
                  </td>
                </tr>
              )}
              {author && (
                <tr>
                  <td className="border border-gray-600 bg-gray-100 px-4 py-2 text-sm font-bold text-center">
                    작성자
                  </td>
                  <td className="border border-gray-600 px-4 py-2 text-sm">
                    {author}
                  </td>
                </tr>
              )}
              {location && (
                <tr>
                  <td className="border border-gray-600 bg-gray-100 px-4 py-2 text-sm font-bold text-center">
                    소재지
                  </td>
                  <td className="border border-gray-600 px-4 py-2 text-sm">
                    {location}
                  </td>
                </tr>
              )}
              {status && (
                <tr>
                  <td className="border border-gray-600 bg-gray-100 px-4 py-2 text-sm font-bold text-center">
                    상태
                  </td>
                  <td className="border border-gray-600 px-4 py-2 text-sm">
                    <Badge variant={status.variant || "default"}>
                      {status.label}
                    </Badge>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* 문서 본문 */}
      <div className="space-y-8 px-8">
        {sections.map((section, index) => (
          <DocumentSectionComponent
            key={index}
            section={section}
            index={index}
          />
        ))}
      </div>

      {/* 문서 하단 */}
      <div className="mt-12 pt-6 border-t-2 border-gray-300 text-center text-sm text-gray-500">
        <div className="flex items-center justify-center gap-2">
          <FileText className="h-4 w-4" />
          <span>공식 문서</span>
        </div>
      </div>
    </div>
  );
}

/**
 * 문서 섹션 컴포넌트
 */
function DocumentSectionComponent({
  section,
  index
}: {
  section: DocumentSection;
  index: number;
}) {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <div className="w-8 h-8 bg-gray-800 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">
          {index + 1}
        </div>
        <h2 className="text-xl font-bold text-gray-900 border-b-2 border-gray-300 pb-1 flex-1">
          {section.title}
        </h2>
      </div>
      <div className="ml-11 bg-gray-50 p-6 rounded-lg border-l-4 border-blue-600">
        {Array.isArray(section.content) ? (
          <DocumentFieldsGrid fields={section.content} />
        ) : (
          <div className="prose prose-gray max-w-none text-gray-800 leading-relaxed">
            {section.content}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 문서 필드 그리드 컴포넌트
 */
function DocumentFieldsGrid({ fields }: { fields: DocumentField[] }) {
  return (
    <div className="border-2 border-gray-300 rounded-lg overflow-hidden">
      <table className="w-full border-collapse">
        <tbody>
          {fields.map((field, index) => (
            <DocumentFieldComponent
              key={index}
              field={field}
              isEven={index % 2 === 0}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
}

/**
 * 문서 필드 컴포넌트
 */
function DocumentFieldComponent({
  field,
  isEven
}: {
  field: DocumentField;
  isEven: boolean;
}) {
  const getFieldIcon = (type: string) => {
    switch (type) {
      case "date":
        return <Calendar className="h-4 w-4 text-gray-500" />;
      case "location":
        return <MapPin className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const rowClass = field.highlight
    ? "bg-yellow-50"
    : isEven
    ? "bg-gray-50"
    : "bg-white";

  return (
    <tr className={`${rowClass} border-b border-gray-200`}>
      <td className="w-1/3 px-4 py-3 border-r border-gray-300 bg-gray-100">
        <div className="flex items-center gap-2">
          {getFieldIcon(field.type || "text")}
          <span className="text-sm font-bold text-gray-800">
            {field.label}
          </span>
        </div>
      </td>
      <td className="px-4 py-3">
        <div className={`text-sm text-gray-900 ${
          field.type === "multiline" ? "whitespace-pre-wrap" : ""
        }`}>
          {field.value || "-"}
        </div>
      </td>
    </tr>
  );
}

export default DocumentView;
