"use client";

import { crtfckey } from "@geon-query/model";
import React from "react";

import List from "../announce/_components/tables/list";

export default function Qna() {
  const [params] = React.useState({
    buldDongNm: "",
    buldFloorNm: "",
    buldHoNm: "",
  });

  return (
    <List
      pnu="4617034028110900032"
      crtfckey={crtfckey}
      numOfRows={10}
      pageNo={1}
      buldDongNm={params.buldDongNm}
      buldFloorNm={params.buldFloorNm}
      buldHoNm={params.buldHoNm}
    />
  );
}
