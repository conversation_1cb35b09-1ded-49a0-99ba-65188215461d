"use client";

import { SidebarProvider, useSidebar } from "@geon-ui/react/primitives/sidebar";
import type { ReactNode } from "react";
import { createContext, useContext, useMemo, useState } from "react";

type ServiceSidebarContextProps = {
  // outer sidebar state
  outer: ReturnType<typeof useSidebar>;

  // inner sidebar state
  innerOpen: boolean;
  setInnerOpen: (open: boolean) => void;

  // navigation state
  active: string | null;
  setActive: (menu: string | null) => void;
  content: ReactNode | null;
  setContent: (content: ReactNode | null) => void;

  // layer-based service selection
  selectedServiceId: string | null;
  setSelectedServiceId: (serviceId: string | null) => void;
};

const ServiceSidebarContext = createContext<ServiceSidebarContextProps | null>(
  null,
);

export function useServiceSidebar() {
  const context = useContext(ServiceSidebarContext);
  if (!context) {
    throw new Error(
      "useServiceSidebar must be used within a ServiceSidebarProvider",
    );
  }
  return context;
}

export function ServiceInnerSidebarProvider({
  children,
}: {
  children: ReactNode;
}) {
  const { innerOpen, setInnerOpen } = useServiceSidebar();

  return (
    <SidebarProvider
      open={innerOpen}
      onOpenChange={setInnerOpen}
      style={
        {
          "--sidebar-width": "28rem",
        } as React.CSSProperties
      }
    >
      {children}
    </SidebarProvider>
  );
}

export function ServiceSidebarProvider({ children }: { children: ReactNode }) {
  const outer = useSidebar();
  const [innerOpen, setInnerOpen] = useState(false);
  const [active, setActive] = useState<string | null>(null);
  const [content, setContent] = useState<React.ReactNode | null>(null);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
    null,
  );

  const contextValue = useMemo(
    () => ({
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
      content,
      setContent,
      selectedServiceId,
      setSelectedServiceId,
    }),
    [
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
      content,
      setContent,
      selectedServiceId,
      setSelectedServiceId,
    ],
  );

  return (
    <ServiceSidebarContext.Provider value={contextValue}>
      {children}
    </ServiceSidebarContext.Provider>
  );
}
