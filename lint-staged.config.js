/** @type {import("lint-staged").Configuration} */
export default {
  "apps/web/**/*.+(ts|tsx)": [
    // type check & lint fix
    (filenames) => `pnpm --filter web eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-ui/react/**/*.+(ts|tsx)": [
    // type check & lint fix
    (filenames) => `pnpm --filter @geon-ui/react eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-map/react-odf/**/*.+(ts|tsx)": [  
    // type check & lint fix
    (filenames) => `pnpm --filter @geon-map/react-odf eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-map/react-ui/**/*.+(ts|tsx)": [  
    // type check & lint fix
    (filenames) => `pnpm --filter @geon-map/react-ui eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-query/model/**/*.+(ts|tsx)": [
    // type check & lint fix
    (filenames) => `pnpm --filter @geon-query/model eslint --fix --cache ${filenames.join(' ')}`,
  ],
  "packages/geon-query/reactQuery/**/*.+(ts|tsx)": [
    // type check & lint fix
    (filenames) => `pnpm --filter @geon-query/react-query eslint --fix --cache ${filenames.join(' ')}`,
  ],
}