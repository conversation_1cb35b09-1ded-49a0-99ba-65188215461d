"use client";
import { useLayer } from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

type HighlightOptions = {
  isFitToLayer?: boolean;
  srid?: string;
  clear?: boolean;
};
type UseHighlightLayerProps = {
  style?: any;
};

export function useHighlightLayer(props: UseHighlightLayerProps = {}) {
  const { style } = props;
  const {
    addLayer,
    addFeature,
    clearFeatures,
    fitToLayer,
    setMaxZIndex,
    updateLayerStyle,
  } = useLayer();
  const [layerId, setLayerId] = useState<string | null>(null);

  useEffect(() => {
    addLayer({ type: "empty" }).then((id: string | void) => {
      if (id) setLayerId(id);
    });
  }, [addLayer]);

  useEffect(() => {
    if (layerId && style) {
      updateLayerStyle(layerId, style);
    }
  }, [layerId]); // updateLayerStyle 제외

  const highlight = useCallback(
    (feature: any, options: HighlightOptions = {}) => {
      console.log("feature 및 options", feature, options);
      if (!feature || !layerId) return;

      const { isFitToLayer = true, srid, clear = true } = options;
      if (clear) clearFeatures(layerId);

      addFeature(layerId, feature, { srid: srid });
      if (isFitToLayer) fitToLayer(layerId, 1000);
      setMaxZIndex(layerId);
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );
  const highlights = useCallback(
    (features: [], options: HighlightOptions = {}) => {
      features.map((feature) => {
        highlight(feature, options);
      });
    },
    [layerId, clearFeatures, addFeature, fitToLayer, setMaxZIndex],
  );

  const clearHighlight = useCallback(() => {
    if (!layerId) return;
    clearFeatures(layerId);
  }, [layerId, clearFeatures]);

  return {
    highlight,
    clearHighlight,
    highlights,
    layerId,
  };
}

