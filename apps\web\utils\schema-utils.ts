import type {
  ApiFieldSchema,
  ApiFieldUiType,
  ApiSchemaResponse,
  ApiSearchMode,
  ClientFieldSchema,
  ClientSearchSchema,
  FieldOption,
  FieldType,
  SearchFieldSchema,
  ServiceSearchSchema,
} from "../components/dynamic-search/types";

/**
 * API uiType을 클라이언트 FieldType으로 매핑
 */
function mapUiTypeToFieldType(
  uiType: ApiFieldUiType,
  searchMode: ApiSearchMode,
): FieldType {
  switch (uiType) {
    case "text":
      return "text";
    case "number":
      return "number";
    case "date":
      return searchMode === "between" ? "dateRange" : "date";
    case "code":
      return "select";
    case "hidden":
      // hidden 필드는 검색폼에 포함하지 않음
      throw new Error("Hidden fields should not be converted to search fields");
    default:
      return "text";
  }
}

// 캐시 저장소
const optionsCache = new Map<string, Promise<FieldOption[]>>();

/**
 * 코드성 데이터의 옵션을 가져오는 함수 (캐싱 적용)
 */
async function fetchCodeOptions(codeGroup: string): Promise<FieldOption[]> {
  // 캐시에서 확인
  if (optionsCache.has(codeGroup)) {
    return optionsCache.get(codeGroup)!;
  }

  // 새로운 요청 생성 및 캐싱
  const fetchPromise = (async () => {
    try {
      const response = await fetch(`/api/common-codes/${codeGroup}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch code options for ${codeGroup}`);
      }
      const data = await response.json();

      // API 응답 형태에 따라 조정 필요
      return data.map((item: any) => ({
        label: item.name || item.label,
        value: item.code || item.value,
      }));
    } catch (error) {
      console.error(`Error fetching code options for ${codeGroup}:`, error);
      return []; // 실패시 빈 배열 반환
    }
  })();

  optionsCache.set(codeGroup, fetchPromise);
  return fetchPromise;
}

/**
 * API 필드 하나를 SearchFieldSchema로 변환
 */
async function convertApiFieldToSearchField(
  apiField: ApiFieldSchema,
): Promise<SearchFieldSchema | null> {
  // hidden 필드나 검색 모드가 none인 경우는 제외
  if (apiField.uiType === "hidden" || apiField.search.mode === "none") {
    return null;
  }

  const fieldType = mapUiTypeToFieldType(apiField.uiType, apiField.search.mode);

  const searchField: SearchFieldSchema = {
    id: apiField.column,
    label: apiField.label,
    type: fieldType,
    order: 0, // API 필드들은 기본적으로 순서 0 (클라이언트 필드보다 앞에 배치)
  };

  // 텍스트 필드의 placeholder 설정
  if (fieldType === "text") {
    searchField.placeholder = `${apiField.label}을(를) 입력하세요`;
  }

  // 숫자 필드의 placeholder 설정
  if (fieldType === "number") {
    searchField.placeholder = `${apiField.label}을(를) 입력하세요`;
  }

  // 코드 타입인 경우 옵션 로딩
  if (fieldType === "select" && apiField.code) {
    const options = await fetchCodeOptions(apiField.code);
    searchField.options = options;
  }

  // dateRange인 경우는 fullWidth로 설정 (두 개의 date picker가 들어가므로)
  if (fieldType === "dateRange") {
    searchField.fullWidth = true;
  }

  return searchField;
}

/**
 * API 스키마의 모든 필드를 SearchFieldSchema 배열로 변환
 */
export async function convertApiFieldsToSearchFields(
  apiFields: ApiFieldSchema[],
): Promise<SearchFieldSchema[]> {
  const convertedFields: SearchFieldSchema[] = [];

  for (const apiField of apiFields) {
    try {
      const searchField = await convertApiFieldToSearchField(apiField);
      if (searchField) {
        convertedFields.push(searchField);
      }
    } catch (error) {
      console.error(`Error converting API field ${apiField.column}:`, error);
    }
  }

  return convertedFields;
}

// API 엔드포인트별 캐시
const apiEndpointCache = new Map<string, Promise<FieldOption[]>>();

/**
 * 동적 옵션 로딩이 필요한 클라이언트 필드 처리 (캐싱 적용)
 */
async function resolveClientField(
  field: ClientFieldSchema,
): Promise<SearchFieldSchema> {
  const searchField: SearchFieldSchema = {
    id: field.id,
    label: field.label,
    type: field.type,
    order: field.order || 100, // 클라이언트 필드들은 기본 순서 100
    placeholder: field.placeholder,
    buttons: field.buttons,
    fullWidth: field.fullWidth,
    defaultValue: field.defaultValue,
  };

  // 동적 로딩이 필요한 select 필드 처리
  if (field.type === "select" && field.apiEndpoint) {
    // 캐시에서 확인
    if (apiEndpointCache.has(field.apiEndpoint)) {
      searchField.options = await apiEndpointCache.get(field.apiEndpoint)!;
    } else {
      // 새로운 요청 생성 및 캐싱
      const fetchPromise = (async () => {
        try {
          const response = await fetch(field.apiEndpoint!);
          if (response.ok) {
            const data = await response.json();
            return data.map((item: any) => ({
              label: item.name || item.label,
              value: item.code || item.value,
            }));
          } else {
            console.error(`Failed to fetch options from ${field.apiEndpoint}`);
            return [];
          }
        } catch (error) {
          console.error(
            `Error fetching options from ${field.apiEndpoint}:`,
            error,
          );
          return [];
        }
      })();

      apiEndpointCache.set(field.apiEndpoint, fetchPromise);
      searchField.options = await fetchPromise;
    }
  } else if (field.options) {
    searchField.options = field.options;
  }

  return searchField;
}

/**
 * 클라이언트 필드들을 SearchFieldSchema 배열로 변환
 */
export async function resolveClientFields(
  clientSchema: ClientSearchSchema,
): Promise<SearchFieldSchema[]> {
  const resolvedFields: SearchFieldSchema[] = [];

  // common 필드들 먼저 처리
  for (const field of clientSchema.common) {
    try {
      const resolvedField = await resolveClientField(field);
      resolvedFields.push(resolvedField);
    } catch (error) {
      console.error(`Error resolving client field ${field.id}:`, error);
    }
  }

  // custom 필드들 처리
  for (const field of clientSchema.custom) {
    try {
      const resolvedField = await resolveClientField(field);
      resolvedFields.push(resolvedField);
    } catch (error) {
      console.error(`Error resolving client field ${field.id}:`, error);
    }
  }

  return resolvedFields;
}

/**
 * API 스키마와 클라이언트 스키마를 합성하여 최종 ServiceSearchSchema 생성
 * activeFields가 제공되면 해당 필드들만 포함하여 스키마 생성
 */
export async function buildSearchSchema(
  apiSchema: ApiSchemaResponse,
  clientSchema: ClientSearchSchema,
  activeFields?: string[],
): Promise<ServiceSearchSchema> {
  // 1. API 필드들을 SearchFieldSchema로 변환
  const apiFields = await convertApiFieldsToSearchFields(apiSchema.fields);

  // 2. 클라이언트 필드들을 SearchFieldSchema로 변환
  const clientFields = await resolveClientFields(clientSchema);

  // 3. 모든 필드 합성
  const allFields = [...apiFields, ...clientFields];

  // 4. activeFields가 제공되면 해당 필드들만 필터링, 그렇지 않으면 defaultFields 사용
  const fieldsToShow = activeFields || clientSchema.defaultFields;
  const filteredFields = allFields.filter((field) =>
    fieldsToShow.includes(field.id),
  );

  // 5. 정렬
  const sortedFields = filteredFields.sort(
    (a, b) => (a.order || 0) - (b.order || 0),
  );

  // 6. 결과 컬럼 스키마는 API에서 제공 (별도 처리 필요시 여기서 변환)
  const resultColumns = apiFields.map((field) => ({
    id: field.id,
    label: field.label,
    accessor: field.id,
    type: "string" as const,
  }));

  return {
    serviceId: apiSchema.resource,
    title: `${apiSchema.resourceName}`,
    fields: sortedFields,
    result: {
      columns: resultColumns,
    },
  };
}

/**
 * 메뉴 ID로 API 스키마를 가져오는 함수
 */
export async function fetchApiSchema(
  menuId: string,
): Promise<ApiSchemaResponse> {
  const response = await fetch(`/api/schema/${menuId}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch API schema for menu ${menuId}`);
  }
  return response.json();
}

/**
 * 추가 가능한 필드 목록을 가져오는 함수
 */
export async function getAvailableFields(
  apiSchema: ApiSchemaResponse,
  clientSchema: ClientSearchSchema,
  currentActiveFields: string[],
): Promise<{ id: string; label: string; type: FieldType }[]> {
  // 1. API 필드들을 SearchFieldSchema로 변환
  const apiFields = await convertApiFieldsToSearchFields(apiSchema.fields);

  // 2. 클라이언트 필드들을 SearchFieldSchema로 변환
  const clientFields = await resolveClientFields(clientSchema);

  // 3. 모든 필드 합성
  const allFields = [...apiFields, ...clientFields];

  // 4. 현재 활성화되지 않은 필드들만 필터링
  const availableFields = allFields.filter(
    (field) => !currentActiveFields.includes(field.id),
  );

  // 5. availableFields 설정이 있으면 해당 필드들만 포함
  const filteredAvailableFields = clientSchema.availableFields
    ? availableFields.filter((field) =>
        clientSchema.availableFields!.includes(field.id),
      )
    : availableFields;

  return filteredAvailableFields.map((field) => ({
    id: field.id,
    label: field.label,
    type: field.type,
  }));
}
