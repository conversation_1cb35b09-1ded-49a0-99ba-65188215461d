"use-client";
import {
  LayerFileDownloadWidget,
  useLayerFileDownload,
} from "@geon-map/react-ui/components";
import type { LayerFileDownloadFormat } from "@geon-map/react-ui/types";
import { type APIRequestType, BASE_URL, crtfckey } from "@geon-query/model";
import {
  createGeonAnalysisClient,
  type GeonAnalysisClient,
} from "@geon-query/model/restapi/analysis";

// API Request Param 명시용 타입(geon-query 외부용)
type LayerFileDownloadParam = APIRequestType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;

interface LayerFileDownloadPackageProps {
  layerFileDownloadInfo?: LayerFileDownloadParam;
  className?: string;
  buttonName?: string | "";
  activeDownloadFormats?: LayerFileDownloadFormat[];
}

export default function LayerFileDownloadPackage({
  layerFileDownloadInfo,
  activeDownloadFormats = [],
  buttonName = "",
  className = "",
}: LayerFileDownloadPackageProps) {
  const apiClient = createGeonAnalysisClient({
    baseUrl: BASE_URL,
    crtfckey: crtfckey,
  });

  const { handleLayerFileDownloadInMemory } = useLayerFileDownload({
    apiClient,
    apiType: "geon",
  });

  return (
    <LayerFileDownloadWidget
      layerFileDownloadInfo={layerFileDownloadInfo}
      activeDownloadFormats={activeDownloadFormats}
      handleLayerFileDownloadInMemory={handleLayerFileDownloadInMemory}
      className={className}
      buttonName={buttonName}
    />
  );
}
