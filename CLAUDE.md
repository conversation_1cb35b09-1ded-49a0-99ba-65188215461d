# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 개요

이 프로젝트는 **무안군 공간정보플랫폼 구축 사업**의 Frontend 프로젝트로, **Turborepo 기반 모노레포**입니다.
GitLab에서 관리되며, Git Subtree 방식으로 packages와 apps가 분리된 저장소 구조를 가집니다.

## 개발 환경 설정

- **패키지 매니저**: pnpm (필수)
- **Node.js**: 20 이상
- **주요 브랜치**: `dev` (개발), `main` (메인)

### 필수 설치
```bash
npm install -g pnpm
corepack use pnpm@latest
```

## 주요 명령어

### 개발 서버 실행
- `pnpm dev` - 모든 apps/packages 개발 서버 실행
- `pnpm web dev` - web 앱만 개발 서버 실행 (포트 3001)

### 빌드 & 테스트
- `pnpm build` - 전체 빌드
- `pnpm lint` - 전체 lint 검사
- `pnpm check-types` - TypeScript 타입 검사
- `pnpm web test` - web 앱 테스트 실행
- `pnpm web analyze` - 번들 사이즈 분석

### 패키지 관리
- `pnpm add -D --filter "web" typescript` - 특정 workspace에 패키지 설치
- `pnpm web` - web workspace 명령어 실행

## 프로젝트 구조

### Apps
- `apps/web/` - 무안군 서비스 프론트엔드 (Next.js 15, React 19)

### Packages (workspace:^ 형태로 import)
- `@geon-map/core` - 지도 관련 core 패키지
- `@geon-map/react-odf` - React 지도 ODF 컴포넌트
- `@geon-map/react-ui` - React 지도 UI 컴포넌트
- `@geon-ui/react` - 공통 React UI 라이브러리
- `@geon-query/model` - 데이터 모델
- `@geon-query/react-query` - React Query 래퍼
- `@config/eslint` - ESLint 설정
- `@config/typescript` - TypeScript 설정
- `@config/tailwind` - TailwindCSS 설정

## 아키텍처 특징

### Git Subtree 구조
- `packages/` 디렉토리는 별도 저장소로 관리
- 공통 packages 변경사항 push: `git subtree push --prefix=packages https://gitlab.geon.kr/geon-biz/magp/pakages.git dev`
- packages 최신 내용 가져오기: `git subtree pull --prefix=packages https://gitlab.geon.kr/geon-biz/magp/pakages.git dev`

### Turborepo 설정
- 병렬 빌드 지원
- 캐싱을 통한 빌드 최적화
- `turbo.json`에서 task 의존성 관리

### 기술 스택
- **Frontend**: Next.js 15, React 19, TypeScript
- **스타일링**: TailwindCSS
- **상태관리**: Zustand
- **지도**: 자체 개발 @geon-map 패키지
- **테스팅**: Jest, Testing Library
- **문서화**: Storybook

## 코드 스타일

- ESLint + Prettier로 코드 포맷팅
- Husky + lint-staged로 pre-commit 검증
- `--max-warnings 0` 설정으로 warning도 오류로 처리
- TypeScript strict 모드 사용

## workspace 패키지 개발시 주의사항

1. 새 패키지는 `pnpm-workspace.yaml`에 등록 필요
2. `package.json`에 `name`, `version`, `exports` 필수 설정
3. 소스 레벨 export 사용 (빌드 불필요)
4. `workspace:^` 방식으로 내부 패키지 참조