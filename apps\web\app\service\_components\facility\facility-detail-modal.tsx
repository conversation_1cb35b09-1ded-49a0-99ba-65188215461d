"use client";

import { Alert, AlertDescription } from "@geon-ui/react/primitives/alert";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { AlertCircle, FileText, Plus } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

import type {
  FacilityDetailData,
  FacilityDetailModalProps,
} from "../../_types/facility-detail";
import { DraggableModal } from "../common/draggable-modal";
import { FacilityDetailForm } from "./facility-detail-form";
import { FacilityDetailView } from "./facility-detail-view";

/**
 * 시설물 상세/등록 공통 모달 컴포넌트
 */
export function FacilityDetailModal({
  isOpen,
  onClose,
  mode,
  facilityData,
  facilityType,
  onRegistered,
}: FacilityDetailModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<FacilityDetailData | null>(
    facilityData || null,
  );

  // 모달이 열릴 때 데이터 로딩
  useEffect(() => {
    if (!isOpen) {
      setError(null);
      setData(null);
      return;
    }

    // 등록 모드이거나 데이터가 이미 있는 경우는 로딩하지 않음
    if (mode === "register" || facilityData) {
      setData(facilityData || null);
      return;
    }

    // 상세 모드에서 데이터 로딩 필요한 경우
    if (mode === "detail" && facilityData && "facilityId" in facilityData) {
      loadFacilityDetail(facilityData.facilityId);
    }
  }, [isOpen, mode, facilityData]);

  /**
   * 시설물 상세 정보 로딩
   */
  const loadFacilityDetail = useCallback(async (facilityId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/facility/${facilityId}`);
      if (!response.ok) {
        throw new Error("시설물 상세 정보를 불러올 수 없습니다");
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "데이터 로딩 실패";
      setError(errorMessage);
      console.error("시설물 상세 정보 로딩 오류:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 등록 완료 처리
   */
  const handleRegisterSubmit = useCallback(
    async (formData: FacilityDetailData) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/facility/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...formData,
            facilityType,
          }),
        });

        if (!response.ok) {
          throw new Error("시설물 등록에 실패했습니다");
        }

        const result = await response.json();

        // 등록 완료 콜백 호출
        onRegistered?.(result.data);

        // 모달 닫기
        onClose();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "등록 실패";
        setError(errorMessage);
        console.error("시설물 등록 오류:", err);
      } finally {
        setLoading(false);
      }
    },
    [facilityType, onRegistered, onClose],
  );

  /**
   * 모달 제목 생성
   */
  const getModalTitle = () => {
    const facilityTypeText = facilityType || "시설물";

    switch (mode) {
      case "detail":
        return (
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {facilityTypeText} 상세 정보
          </div>
        );
      case "register":
        return (
          <div className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            {facilityTypeText} 등록
          </div>
        );
      default:
        return "시설물 정보";
    }
  };

  /**
   * 모달 콘텐츠 렌더링
   */
  const renderModalContent = () => {
    // 로딩 중
    if (loading) {
      return (
        <div className="space-y-4 p-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-20 w-full" />
        </div>
      );
    }

    // 에러 상태
    if (error) {
      return (
        <div className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      );
    }

    // 모드별 컴포넌트 렌더링
    switch (mode) {
      case "detail":
        if (!data) {
          return (
            <div className="p-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  표시할 시설물 정보가 없습니다.
                </AlertDescription>
              </Alert>
            </div>
          );
        }
        return <FacilityDetailView data={data} loading={loading} />;

      case "register":
        return (
          <FacilityDetailForm
            facilityType={facilityType}
            onSubmit={handleRegisterSubmit}
            onCancel={onClose}
            loading={loading}
          />
        );

      default:
        return null;
    }
  };

  return (
    <DraggableModal isOpen={isOpen} onClose={onClose} title={getModalTitle()}>
      {renderModalContent()}
    </DraggableModal>
  );
}

export default FacilityDetailModal;
