"use client";

import { DndContext, useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { Alert, AlertDescription } from "@geon-ui/react/primitives/alert";
import { Button } from "@geon-ui/react/primitives/button";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { AlertCircle, FileText, Plus, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";

import type {
  FacilityDetailData,
  FacilityDetailModalProps,
} from "../../_types/facility-detail";
import { FacilityDetailForm } from "./facility-detail-form";
import { FacilityDetailView } from "./facility-detail-view";

/**
 * 드래그 가능한 모달 콘텐츠 컴포넌트
 */
function DraggableModalContent({
  children,
  onClose,
  title,
  isOpen,
}: {
  children: React.ReactNode;
  onClose: () => void;
  title: React.ReactNode;
  isOpen: boolean;
}) {
  const [position, setPosition] = useState({ x: 0, y: 0 });

  // 모달이 닫힐 때 위치 초기화
  useEffect(() => {
    if (!isOpen) {
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen]);

  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: "facility-modal",
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    left: '50%',
    top: '50%',
    marginLeft: `${position.x}px`,
    marginTop: `${position.y}px`,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="fixed z-50 -translate-x-1/2 -translate-y-1/2"
    >
      <div className="max-h-[90vh] w-full max-w-4xl rounded-lg border bg-white shadow-lg">
        {/* 드래그 핸들 헤더 */}
        <div
          {...listeners}
          {...attributes}
          className="flex cursor-grab select-none items-center justify-between rounded-t-lg border-b bg-gray-50 p-4 active:cursor-grabbing"
        >
          <div className="flex-1">
            {title}
            <div className="ml-4 text-xs text-gray-400">드래그하여 이동</div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="ml-2 h-auto p-1 hover:bg-gray-200"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* 모달 콘텐츠 */}
        <div className="max-h-[calc(90vh-80px)] overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
}

/**
 * 시설물 상세/등록 공통 모달 컴포넌트
 */
export function FacilityDetailModal({
  isOpen,
  onClose,
  mode,
  facilityData,
  facilityType,
  onRegistered,
}: FacilityDetailModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<FacilityDetailData | null>(
    facilityData || null,
  );

  // 모달이 열릴 때 데이터 로딩
  useEffect(() => {
    if (!isOpen) {
      setError(null);
      setData(null);
      return;
    }

    // 등록 모드이거나 데이터가 이미 있는 경우는 로딩하지 않음
    if (mode === "register" || facilityData) {
      setData(facilityData || null);
      return;
    }

    // 상세 모드에서 데이터 로딩 필요한 경우
    if (mode === "detail" && facilityData?.facilityId) {
      loadFacilityDetail(facilityData.facilityId);
    }
  }, [isOpen, mode, facilityData]);

  /**
   * 시설물 상세 정보 로딩
   */
  const loadFacilityDetail = useCallback(async (facilityId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/facility/${facilityId}`);
      if (!response.ok) {
        throw new Error("시설물 상세 정보를 불러올 수 없습니다");
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "데이터 로딩 실패";
      setError(errorMessage);
      console.error("시설물 상세 정보 로딩 오류:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 등록 완료 처리
   */
  const handleRegisterSubmit = useCallback(
    async (formData: FacilityDetailData) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch("/api/facility/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...formData,
            facilityType,
          }),
        });

        if (!response.ok) {
          throw new Error("시설물 등록에 실패했습니다");
        }

        const result = await response.json();

        // 등록 완료 콜백 호출
        onRegistered?.(result.data);

        // 모달 닫기
        onClose();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "등록 실패";
        setError(errorMessage);
        console.error("시설물 등록 오류:", err);
      } finally {
        setLoading(false);
      }
    },
    [facilityType, onRegistered, onClose],
  );

  /**
   * 모달 제목 생성
   */
  const getModalTitle = () => {
    const facilityTypeText = facilityType || "시설물";

    switch (mode) {
      case "detail":
        return (
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {facilityTypeText} 상세 정보
          </div>
        );
      case "register":
        return (
          <div className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            {facilityTypeText} 등록
          </div>
        );
      default:
        return "시설물 정보";
    }
  };

  /**
   * 모달 콘텐츠 렌더링
   */
  const renderModalContent = () => {
    // 로딩 중
    if (loading) {
      return (
        <div className="space-y-4 p-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-4 w-2/3" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/2" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-20 w-full" />
        </div>
      );
    }

    // 에러 상태
    if (error) {
      return (
        <div className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      );
    }

    // 모드별 컴포넌트 렌더링
    switch (mode) {
      case "detail":
        if (!data) {
          return (
            <div className="p-6">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  표시할 시설물 정보가 없습니다.
                </AlertDescription>
              </Alert>
            </div>
          );
        }
        return <FacilityDetailView data={data} loading={loading} />;

      case "register":
        return (
          <FacilityDetailForm
            facilityType={facilityType}
            onSubmit={handleRegisterSubmit}
            onCancel={onClose}
            loading={loading}
          />
        );

      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <DndContext
      onDragEnd={(event) => {
        // 드래그 종료 시 위치 고정 - DraggableModalContent 내부에서 처리
        console.log('Drag ended:', event.delta);
      }}
    >
      <DraggableModalContent 
        onClose={onClose} 
        title={getModalTitle()}
        isOpen={isOpen}
      >
        {renderModalContent()}
      </DraggableModalContent>
    </DndContext>
  );
}

export default FacilityDetailModal;
