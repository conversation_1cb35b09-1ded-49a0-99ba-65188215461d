"use client";

import React, { createContext, useContext, useEffect } from "react";

import { setGlobalControlsConfig } from "../stores/controls-config-store";
import type { DrawControlOptions } from "../types/draw-types";

// 측정 옵션 타입은 core에서 직접 관리하므로 any로 수용
type MeasureControlOptions = any;

/**
 * Controls 설정 옵션 (Dependency Injection용)
 */
export interface ControlsConfig {
  /** Draw Control 초기화 옵션 */
  drawOptions?: DrawControlOptions;
  /** Measure Control 초기화 옵션 */
  measureOptions?: MeasureControlOptions;
  /** Clear Control 초기화 옵션 */
  clearOptions?: {
    clearAll?: boolean;
  };
  /** Basemap Control 초기화 옵션 */
  basemapOptions?: {
    basemapList?: any;
    urls?: any;
  };
  /** Scale Control 초기화 옵션 */
  scaleOptions?: {
    size?: number;
    scaleInput?: boolean;
  };
  /** Overview Control 초기화 옵션 */
  overviewOptions?: {
    enabled?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

export interface ControlsProviderOptions extends ControlsConfig {}

// Controls Config Context 생성
const ControlsConfigContext = createContext<ControlsConfig | null>(null);

/**
 * 🎯 ControlsProvider (Controls 설정 제공자)
 *
 * Controls 설정을 Context로 제공하는 순수한 Provider입니다.
 * 실제 초기화는 Map 컴포넌트에서 수행됩니다. (Dependency Injection 패턴)
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider
 *     drawOptions={{ tools: ["polygon", "point"] }}
 *     measureOptions={{ tools: ["distance", "area"] }}
 *     basemapOptions={{ basemapList: [...] }}
 *   >
 *       <MapContainer />
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function ControlsProvider({
  children,
  drawOptions = {
    continuity: false,
    createNewLayer: false,
    tools: ["text", "polygon", "lineString", "box", "point", "circle", "curve"],
  },
  measureOptions = {
    tools: ["distance", "area", "round", "spot"],
    continuity: false,
    rightClickDelete: false,
  },
  clearOptions = { clearAll: true },
  basemapOptions = {},
  scaleOptions = { size: 100, scaleInput: false },
  overviewOptions = { enabled: true },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ControlsProviderOptions>) {
  // 🎯 설정만 Context로 제공 (초기화 로직 없음)
  const config: ControlsConfig = {
    drawOptions,
    measureOptions,
    clearOptions,
    basemapOptions,
    scaleOptions,
    overviewOptions,
    autoInitialize,
    onError,
  };

  // 🎯 전역 Store에도 설정 저장 (React Context 외부에서 접근 가능)
  useEffect(() => {
    setGlobalControlsConfig(config);

    // 컴포넌트 언마운트 시 정리
    return () => {
      setGlobalControlsConfig(null);
    };
  }, [config]);

  return (
    <ControlsConfigContext.Provider value={config}>
      {children}
    </ControlsConfigContext.Provider>
  );
}

/**
 * Controls 설정에 접근하는 훅
 */
export function useControlsConfig(): ControlsConfig | null {
  return useContext(ControlsConfigContext);
}
