"use client";

import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import { createColumnHelper } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import React from "react";

import Pagination from "@/components/table/pagination";
import ViewTable from "@/components/table/view";

export default function BuildingHoTable({
  client,
  ...props
}: APIRequestType<EstateClient["building"]["ho"]> & {
  client: EstateClient;
}) {
  // message handler
  const t = useTranslations("estate.building.ho");
  // Pagination States
  const [numOfRows, setNumOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo, setPageNo] = React.useState<number>(props.pageNo);

  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["building"]["ho"]>
  >({
    queryKey: ["building/ho", { ...props, numOfRows, pageNo }],
    queryFn: () => client.building.ho({ ...props, numOfRows, pageNo }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError || !data || typeof data.result === "string")
    return (
      <div className="text-destructive flex justify-center align-middle">
        Error loading parcel data: {error as string}
        {data && `, ${data?.result as unknown as string}`}
      </div>
    );

  const helper = createColumnHelper<(typeof data.result.resultList)[0]>();
  const columns = [
    helper.accessor("buldNm", {
      cell: (info) => info.getValue(),
      header: t("buldNm"),
    }),
    helper.accessor("buldDongNm", {
      cell: (info) => info.getValue(),
      header: t("buldDongNm"),
    }),
    helper.accessor("buldFloorNm", {
      cell: (info) => info.getValue(),
      header: t("buldFloorNm"),
    }),
    helper.accessor("buldHoNm", {
      cell: (info) => info.getValue(),
      header: t("buldHoNm"),
    }),
    helper.accessor("regstrSeCodeNm", {
      cell: (info) => info.getValue(),
      header: t("regstrSeCodeNm"),
    }),
    helper.accessor("ldaQotaRate", {
      cell: (info) => info.getValue(),
      header: t("ldaQotaRate"),
    }),
  ];

  return (
    <div className="flex w-full flex-col overflow-hidden overflow-y-auto">
      <ViewTable data={data.result.resultList} columns={columns} pinHeader />
      {typeof data?.result !== "string" && data?.result.pageInfo && (
        <Pagination
          pageInfo={data.result.pageInfo}
          onPageNoChange={setPageNo}
          onNumOfRowsChange={(newNumOfRows) => {
            setNumOfRows(newNumOfRows);
            setPageNo(1);
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
