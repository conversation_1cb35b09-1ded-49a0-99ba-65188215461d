import { Button } from "@geon-ui/react/primitives/button";
import type { Meta, StoryObj } from "@storybook/nextjs";
import { MenuSquare } from "lucide-react";
import { fn } from "storybook/test";

const meta = {
  title: "Shadcn/Button",
  component: Button,
  tags: ["autodocs"],
  args: {
    onClick: fn(),
  },
  argTypes: {
    variant: {
      control: "inline-radio",
      description: "버튼의 색상 및 형태, 강조",
      options: [
        undefined,
        "default",
        "secondary",
        "outline",
        "destructive",
        "ghost",
        "link",
      ],
    },
    size: {
      control: "inline-radio",
      description: "버튼의 크기",
      options: [undefined, "default", "sm", "lg", "icon"],
    },
    asChild: {
      control: false,
      description: "DOM 이벤트를 자식 node 에게 상속",
    },
    children: { control: false, table: { type: { summary: "ReactNode" } } },
    onClick: { control: false },
  },
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: "Default Button",
  },
};

export const Variant: Story = {
  argTypes: {
    variant: { control: false },
  },
  render: (args: any) => (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-center gap-2">
        <Button {...args} variant="default">
          default
        </Button>
        <Button {...args} variant="secondary">
          secondary
        </Button>
        <Button {...args} variant="outline">
          outline
        </Button>
        <Button {...args} variant="destructive">
          destructive
        </Button>
      </div>
      <div className="flex items-center justify-center gap-2">
        <Button {...args} variant="ghost">
          ghost
        </Button>
        <Button {...args} variant="link">
          link
        </Button>
      </div>
    </div>
  ),
};

export const Size: Story = {
  argTypes: {
    size: { control: false },
  },
  render: (args: any) => (
    <div className="flex items-center gap-2">
      <Button {...args} size="sm">
        sm
      </Button>
      <Button {...args} size="default">
        default
      </Button>
      <Button {...args} size="lg">
        lg
      </Button>
      <Button {...args} size="icon" title="icon">
        <MenuSquare />
      </Button>
    </div>
  ),
};
