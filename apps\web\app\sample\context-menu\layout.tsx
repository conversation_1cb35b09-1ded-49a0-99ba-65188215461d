import { BasemapProvider, MapProvider } from "@geon-map/react-odf";
import React from "react";

import TestContextMenu from "./_components/test-context-menu";

export default function StatisticsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
      <BasemapProvider />
      <TestContextMenu>{children}</TestContextMenu>
    </MapProvider>
  );
}
