import type { FacilityType, ServiceFacilityGroup } from "../_types/facility";

// 서비스별 시설물 유형 구성 데이터
export const SERVICE_FACILITIES: ServiceFacilityGroup[] = [
  {
    serviceId: "road",
    title: "도로",
    facilityTypes: [
      // id 는 실제 layer id 값이 되어야 함.
      // 구성 데이터를 서버단에서 동적으로 가져올 지, 클라이언트에서 상수로 관리할지 정해야 함.
      {
        id: "road-bridge",
        title: "가로등",
        description: "도로 가로등 시설물",
        serviceId: "road-bridge",
        enabled: true,
        category: "구조물",
        order: 1,
        color: "#3B82F6", // blue
      },
      {
        id: "road-tunnel",
        title: "가로수",
        description: "도로 가로수 시설물",
        serviceId: "road-tunnel",
        enabled: true,
        category: "구조물",
        order: 2,
        color: "#8B5CF6", // violet
      },
      {
        id: "road-safety",
        title: "보안등",
        description: "도로 보안등 시설물",
        serviceId: "road-safety",
        enabled: true,
        category: "안전",
        order: 3,
        color: "#F59E0B", // amber
      },
      {
        id: "road-drainage",
        title: "자전거도로",
        description: "자전거도로",
        serviceId: "road-drainage",
        enabled: true,
        category: "배수",
        order: 4,
        color: "#10B981", // emerald
      },
    ],
  },
];

// Helper functions
export function getServiceFacilities(serviceId: string): FacilityType[] {
  const service = SERVICE_FACILITIES.find((s) => s.serviceId === serviceId);
  return service?.facilityTypes || [];
}

export function getFacilityById(facilityId: string): FacilityType | null {
  for (const service of SERVICE_FACILITIES) {
    const facility = service.facilityTypes.find((f) => f.id === facilityId);
    if (facility) return facility;
  }
  return null;
}

export function getFacilitiesByCategory(
  serviceId: string,
): Record<string, FacilityType[]> {
  const facilities = getServiceFacilities(serviceId);
  return facilities.reduce(
    (groups, facility) => {
      const category = facility.category || "기타";
      if (!groups[category]) groups[category] = [];
      groups[category].push(facility);
      return groups;
    },
    {} as Record<string, FacilityType[]>,
  );
}

export function getFacilitiesByIds(facilityIds: string[]): FacilityType[] {
  const results: FacilityType[] = [];
  for (const id of facilityIds) {
    const facility = getFacilityById(id);
    if (facility) results.push(facility);
  }
  return results;
}
