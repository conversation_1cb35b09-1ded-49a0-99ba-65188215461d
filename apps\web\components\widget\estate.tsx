import { BASE_URL, createEstateClient, crtfckey } from "@geon-query/model";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@geon-ui/react/primitives/dropdown-menu";
import { Building } from "lucide-react";

export default function EstateWidget({ className }: { className?: string }) {
  const client = createEstateClient({
    baseUrl: BASE_URL,
    crtfckey,
  });

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="secondary" size="icon" title="부동산 정보 API">
            <Building />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="z-[9999] w-[220px]">
          <DropdownMenuLabel className="font-bold">
            건물 조회(일필지)
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.building.floor({
                agbldgSn: "0001",
                buldDongNm: "1",
                buldFloorNm: "1",
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            건물 층 수 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.building.ho({
                agbldgSn: "0001",
                buldDongNm: "1",
                buldFloorNm: "1",
                buldHoNm: "112",
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            건물 호 수 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.building.ho({
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            대지권 등록 목록 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.building.serial({
                agbldgSn: "0001",
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            건물 일련번호 조회
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="font-bold">
            건축물 대장 정보 조회
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.registry.floor({
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            층별개요 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.registry.general({
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            총괄표제부 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.registry.headings({
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            표제부 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.registry.ownership({
                crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            전유부 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.registry.area({
                // crtfckey,
                numOfRows: 1,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            전유공유면적 조회
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="font-bold">
            토지 조회 (일필지)
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.land.basic({
                crtfckey,
                pnu: "1111010100100010000",
                numOfRows: 10,
                pageNo: 1,
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            토지임야 목록 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.land.characteristics({
                crtfckey,
                numOfRows: 1,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            토지특성 속성 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.land.history({
                startDt: "19480501",
                endDt: "20161231",
                crtfckey,
                numOfRows: 1,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            토지이동이력 속성 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.land.ownership({
                crtfckey,
                numOfRows: 1,
                pageNo: 1,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            토지소유정보 속성 조회
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="font-bold">
            토지이용계획 조회 (일필지)
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.land.useplan({
                cnflcAt: "1",
                crtfckey,
                numOfRows: 1,
                pageNo: 1,
                pnu: "1111010100100010000",
                prposAreaDstrcCodeNm: "아파트지구",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            토지이용계획 속성 조회
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="font-bold">
            일필지 종합 정보
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.parcel.all({
                // crtfckey,
                pnu: "1111010100100010000",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            일필지 종합 정보 조회
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuLabel className="font-bold">
            가격 정보 조회(일필지)
          </DropdownMenuLabel>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.price.apt({
                dongNm: "101",
                hoNm: "201",
                // crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1144012700116340000",
                stdrYear: "2012",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            공동주택가격 속성 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.price.ind({
                // crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111016700100200000",
                stdrYear: "2012",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            개별주택가격 속성 조회
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={async () => {
              const result = await client.price.ind({
                // crtfckey,
                numOfRows: 10,
                pageNo: 1,
                pnu: "1111016700100200000",
                stdrYear: "2012",
              });
              console.log("🚀 ~ EstateWidget ~ result:", result);
            }}
          >
            개별공시지가 속성 조회
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
