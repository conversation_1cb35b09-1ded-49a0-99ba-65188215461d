// app/board/announce/layout.tsx
import { QueryProvider } from "@geon-query/react-query";
import { SidebarProvider } from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { ServiceSidebarProvider } from "../service/_contexts/sidebar";
import OuterSidebar from "./_components/sidebar/outer";

export default function BoardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <ServiceSidebarProvider>
        <OuterSidebar />
        <QueryProvider>
          <section>
            <h1>게시판 레이아웃</h1>
            <div>{children}</div>
          </section>
        </QueryProvider>
      </ServiceSidebarProvider>
    </SidebarProvider>
  );
}
