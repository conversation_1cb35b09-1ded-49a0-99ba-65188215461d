"use client";

import { BasemapInfo } from "@geon-map/core";
import {
  DEFAULT_BASE_MAPS,
  SwiperBasemapWidget,
  useSwiperContext,
} from "@geon-map/react-ui/components";
import { BasemapListRequest, defaultGeonSmtClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { useCallback, useState } from "react";

export default function SwiperBasemapWidgetUse() {
  const { dispatch } = useSwiperContext();

  const [value, onValueChange] = useState<BasemapInfo | undefined>(
    DEFAULT_BASE_MAPS[1],
  );

  const [params] = useState<BasemapListRequest>({
    pageIndex: 1,
    pageSize: 100,
  });

  const { data } = useAppQuery({
    queryKey: ["basemapList", params],
    queryFn: () => defaultGeonSmtClient.basemap.list(params),
  });

  const handleBasemapSelect = useCallback(
    (swiperBasemapInfo: BasemapInfo) => {
      onValueChange(swiperBasemapInfo);
      dispatch({ type: "SET_LEFT_BASEMAP_INFO", swiperBasemapInfo });
    },
    [dispatch],
  );

  const handleHybridToggle = useCallback(
    (checked: boolean) => {
      dispatch({ type: "SET_HYBRID_STATUS", hybrid: checked });
    },
    [dispatch],
  );

  return (
    <SwiperBasemapWidget
      selectBasemap={value}
      onValueChange={handleBasemapSelect}
      onHybridChange={handleHybridToggle}
      baseMaps={data?.result.list}
    />
  );
}
