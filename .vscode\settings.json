{
  // cspell:
  "cSpell.files": [
    "**/*.js",
    "**/*.jsx",
    "**/*.ts",
    "**/*.tsx",
    "**/*.json",
    "**/*.md"
  ],
  "cSpell.words": [
    "Ablty",
    "addrgeo",
    "Administ",
    "agbldg",
    "Allshare",
    "anals",
    "aphus",
    "Atch",
    "autodocs",
    "baro",
    "baroemap",
    "Basemap",
    "bbox",
    "bjdong",
    "Bkmk",
    "Bldrgst",
    "buld",
    "bylot",
    "cmdk",
    "cmmn",
    "cnflc",
    "cnrs",
    "crtfckey",
    "crtn",
    "ctprvn",
    "Ctpv",
    "donwload",
    "Dsgn",
    "dsslve",
    "Dstrc",
    "dvsion",
    "Elvt",
    "Embla",
    "emgen",
    "EPSG",
    "Ersr",
    "Estm",
    "Exct",
    "extrc",
    "Flter",
    "fmly",
    "Frtl",
    "fullshare",
    "geoco",
    "geofile",
    "Geoserver",
    "geotiff",
    "grnd",
    "heit",
    "hhld",
    "hotspot",
    "indr",
    "Inqire",
    "instt",
    "intsct",
    "jibun",
    "juso",
    "Kakao",
    "landbundle",
    "lndcgr",
    "lndpcl",
    "lyrgrp",
    "magp",
    "mnnm",
    "MULTIPOLYGON",
    "Mvmn",
    "ngisair",
    "nrby",
    "ntice",
    "nums",
    "opblock",
    "Opertntcn",
    "oudr",
    "ovrlay",
    "ownship",
    "pblntf",
    "pclnd",
    "Pkng",
    "pmsno",
    "posesn",
    "prpos",
    "Prvonsh",
    "prvuse",
    "pttrn",
    "Pubuse",
    "Purps",
    "Qota",
    "regstr",
    "resdnc",
    "restapi",
    "rnum",
    "Roadview",
    "rserthqk",
    "serviceworker",
    "shadcn",
    "sigungu",
    "Sittn",
    "Slno",
    "Sonner",
    "splot",
    "Srid",
    "stcns",
    "stdr",
    "strct",
    "sumry",
    "tmplat",
    "tpgrph",
    "turbopack",
    "Turborepo",
    "Ugrnd",
    "unproject",
    "Updt",
    "useplan",
    "usermap",
    "Utcnt",
    "vaul",
    "vworld",
    "webmap",
    "wmts"
  ],
  // editor:
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  // tailwindcss
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "tailwindCSS.experimental.configFile": "packages/config/tailwind/globals.css",
  // eslint:
  "eslint.enable": true,
  "eslint.format.enable": true,
  "[css]": {
    "editor.suggest.insertMode": "replace",
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[javascript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "editor.formatOnSave": true
  },
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "editor.formatOnSave": true
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint",
    "editor.formatOnSave": true
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[markdown]": {
    "editor.defaultFormatter": "DavidAnson.vscode-markdownlint",
    "editor.formatOnSave": true,

    "editor.unicodeHighlight.ambiguousCharacters": false,
    "editor.unicodeHighlight.invisibleCharacters": false,
    "diffEditor.ignoreTrimWhitespace": false,
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
      "comments": "off",
      "strings": "off",
      "other": "off"
    },
    "cSpell.fixSpellingWithRenameProvider": true,
    "cSpell.advanced.feature.useReferenceProviderWithRename": true,
    "cSpell.advanced.feature.useReferenceProviderRemove": "/^#+\\s/"
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/*.code-search": true
  },
  // prettier:
  "prettier.singleQuote": false,
  "prettier.quoteProps": "preserve"
}
