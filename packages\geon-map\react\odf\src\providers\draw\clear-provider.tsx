"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";

/**
 * ClearProvider 설정 옵션
 */
export interface ClearProviderOptions {
  /** Clear Control 초기화 옵션 */
  clearOptions?: {
    clearAll?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 ClearProvider (Clear Control 설정 전용)
 *
 * Clear Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <ClearProvider clearOptions={{ clearAll: true }}>
 *       <ClearButton />
 *     </ClearProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function ClearProvider({
  children,
  clearOptions = { clearAll: true },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ClearProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Clear 설정 등록
    updateConfig({
      clearOptions,
      autoInitialize,
      onError,
    });
  }, [clearOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
