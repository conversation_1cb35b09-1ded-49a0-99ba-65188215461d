import { Event, MapSystemEventType } from "@geon-map/core";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { logger } from "./middleware/logger";

/**
 * 🎯 순수 이벤트 관리 Store
 *
 * 이벤트 등록/해제와 관련된 메타데이터만 관리합니다.
 * 비즈니스 상태(좌표, 줌 등)는 각각의 전용 Store에서 관리합니다.
 */

// 순수 이벤트 관리 상태 인터페이스
export interface EventManagementState {
  // ODF Event 인스턴스 (전역 1개)
  eventInstance: null | Event;

  // 이벤트 리스너 상태 (메타데이터만)
  activeListeners: Map<string, string>; // listenerId -> eventId
  listenerMetadata: Map<
    string,
    {
      target: string;
      eventType: string;
      registeredAt: number;
    }
  >; // listenerId -> metadata

  // 에러 상태
  eventErrors: Map<string, Error>; // listenerId -> error
}

// 순수 이벤트 관리 액션 인터페이스
export interface EventManagementActions {
  // 이벤트 리스너 관리
  addActiveListener: (
    listenerId: string,
    eventId: string,
    metadata: {
      target: string;
      eventType: string;
    },
  ) => void;
  removeActiveListener: (listenerId: string) => void;
  clearAllListeners: () => void;

  // 에러 관리
  setEventError: (listenerId: string, error: Error | null) => void;
  clearEventError: (listenerId: string) => void;
  clearAllErrors: () => void;

  // 전체 리셋
  reset: () => void;

  // Event 인스턴스 설정
  setEventInstance: (instance: any | Event) => void;

  // 🎯 이벤트 등록/해제 비즈니스 로직 (Store에서 관리)
  registerEventListener: (
    target: any,
    eventType: MapSystemEventType,
    handler: (event: any) => void,
    options?: { listenerId?: string; once?: boolean },
  ) => {
    listenerId: string;
    eventId: string;
    cleanup: () => void;
  };

  unregisterEventListener: (listenerId: string) => void;
  cleanupAllEventListeners: () => void;

  // 디버깅용 메서드
  getListenerInfo: () => {
    totalListeners: number;
    listenersByType: Record<string, number>;
    oldestListener: { listenerId: string; age: number } | null;
  };
}

type EventStore = EventManagementState & EventManagementActions;

// 초기 상태
const initialState: EventManagementState = {
  eventInstance: null,
  activeListeners: new Map(),
  listenerMetadata: new Map(),
  eventErrors: new Map(),
};

/**
 * 🎯 이벤트 스토어 팩토리 함수 (다중 지도 인스턴스 지원)
 */
export const createEventStore = (
  initialOptions: Partial<EventManagementState> = {},
) => {
  const mergedInitialState = {
    ...initialState,
    ...initialOptions,
  };

  return create<EventStore>()(
    devtools(
      logger(
        (set, get) => ({
          ...mergedInitialState,

          // 이벤트 리스너 관리
          addActiveListener: (listenerId, eventId, metadata) => {
            const { activeListeners, listenerMetadata } = get();
            const newListeners = new Map(activeListeners);
            const newMetadata = new Map(listenerMetadata);

            newListeners.set(listenerId, eventId);
            newMetadata.set(listenerId, {
              ...metadata,
              registeredAt: Date.now(),
            });

            set(
              {
                activeListeners: newListeners,
                listenerMetadata: newMetadata,
              },
              false,
              "addActiveListener",
            );
          },

          removeActiveListener: (listenerId) => {
            const { activeListeners, listenerMetadata } = get();
            const newListeners = new Map(activeListeners);
            const newMetadata = new Map(listenerMetadata);

            newListeners.delete(listenerId);
            newMetadata.delete(listenerId);

            set(
              {
                activeListeners: newListeners,
                listenerMetadata: newMetadata,
              },
              false,
              "removeActiveListener",
            );
          },

          clearAllListeners: () =>
            set(
              {
                activeListeners: new Map(),
                listenerMetadata: new Map(),
              },
              false,
              "clearAllListeners",
            ),

          // 에러 관리
          setEventError: (listenerId, error) => {
            const { eventErrors } = get();
            const newErrors = new Map(eventErrors);
            if (error) {
              newErrors.set(listenerId, error);
            } else {
              newErrors.delete(listenerId);
            }
            set({ eventErrors: newErrors }, false, "setEventError");
          },

          clearEventError: (listenerId) => {
            const { eventErrors } = get();
            const newErrors = new Map(eventErrors);
            newErrors.delete(listenerId);
            set({ eventErrors: newErrors }, false, "clearEventError");
          },

          clearAllErrors: () =>
            set({ eventErrors: new Map() }, false, "clearAllErrors"),

          // 전체 리셋
          reset: () => set(initialState, false, "reset"),

          // Event 인스턴스 설정
          setEventInstance: (instance: Event) =>
            set({ eventInstance: instance }, false, "setEventInstance"),

          // 디버깅용 메서드
          getListenerInfo: () => {
            const { activeListeners, listenerMetadata } = get();
            const totalListeners = activeListeners.size;

            // 이벤트 타입별 카운트
            const listenersByType: Record<string, number> = {};
            let oldestListener: { listenerId: string; age: number } | null =
              null;
            let oldestTime = Date.now();

            listenerMetadata.forEach((metadata, listenerId) => {
              // 타입별 카운트
              listenersByType[metadata.eventType] =
                (listenersByType[metadata.eventType] || 0) + 1;

              // 가장 오래된 리스너 찾기
              if (metadata.registeredAt < oldestTime) {
                oldestTime = metadata.registeredAt;
                oldestListener = {
                  listenerId,
                  age: Date.now() - metadata.registeredAt,
                };
              }
            });

            return {
              totalListeners,
              listenersByType,
              oldestListener,
            };
          },

          // 🎯 이벤트 등록/해제 비즈니스 로직 (Store에서 관리)
          registerEventListener: (
            target: any,
            eventType: MapSystemEventType,
            handler: (event: any) => void,
            options: { listenerId?: string; once?: boolean } = {},
          ) => {
            const {
              listenerId = `${eventType}_${Date.now()}_${Math.random()}`,
              once = false,
            } = options;

            try {
              const { eventInstance } = get();
              if (!eventInstance || !target) {
                throw new Error("EventInstance or target is not available");
              }

              // 중복 등록 방지: 동일 listenerId가 이미 있으면 기존 것을 반환
              const { activeListeners } = get();
              const existingEventId = activeListeners.get(listenerId);
              if (existingEventId) {
                return {
                  listenerId,
                  eventId: existingEventId,
                  cleanup: () => {
                    try {
                      eventInstance.removeListener(existingEventId);
                      get().removeActiveListener(listenerId);
                    } catch (error) {
                      console.error(
                        "Failed to cleanup existing event listener:",
                        error,
                      );
                    }
                  },
                };
              }

              const eventId = eventInstance.addListener(
                target,
                eventType,
                handler,
                once,
              );

              // Store에 등록된 리스너 추가
              get().addActiveListener(listenerId, eventId, {
                target: target.constructor?.name || "Unknown",
                eventType,
              });
              get().clearEventError(listenerId);

              return {
                listenerId,
                eventId,
                cleanup: () => {
                  try {
                    eventInstance.removeListener(eventId);
                    get().removeActiveListener(listenerId);
                  } catch (error) {
                    console.error("Failed to cleanup event listener:", error);
                  }
                },
              };
            } catch (error) {
              const err =
                error instanceof Error ? error : new Error(String(error));
              get().setEventError(listenerId, err);
              throw err;
            }
          },

          // 🎯 특정 리스너 제거 (Store 내부 eventInstance 사용)
          unregisterEventListener: (listenerId: string) => {
            const { activeListeners, eventInstance } = get();
            const eventId = activeListeners.get(listenerId);

            if (eventId && eventInstance) {
              try {
                eventInstance.removeListener(eventId);
                get().removeActiveListener(listenerId);
                get().clearEventError(listenerId);
              } catch (error) {
                console.error("Failed to unregister event listener:", error);
                get().setEventError(
                  listenerId,
                  error instanceof Error ? error : new Error(String(error)),
                );
              }
            }
          },

          // 🎯 모든 리스너 정리 (Store 내부 eventInstance 사용)
          cleanupAllEventListeners: () => {
            const { activeListeners, eventInstance } = get();

            activeListeners.forEach((eventId, listenerId) => {
              try {
                if (eventInstance) {
                  eventInstance.removeListener(eventId);
                }
              } catch (error) {
                console.error(
                  `Failed to cleanup listener ${listenerId}:`,
                  error,
                );
              }
            });

            get().clearAllListeners();
            get().clearAllErrors();
          },
        }),
        "event-store",
      ),
      { name: "event-store" },
    ),
  );
};

/**
 * 🎯 전역 이벤트 스토어 (하위 호환성)
 *
 * 기존 코드와의 호환성을 위해 유지합니다.
 * 단일 지도 사용 시에는 이 스토어를 사용합니다.
 */
export const useEventStore = createEventStore();

/**
 * 🎯 범용 이벤트 리스너 등록/해제 액션 (Store 기반)
 *
 * 모든 이벤트 등록은 이 액션을 통해 Store에서 관리됩니다.
 */
export const useEventStoreActions = () => {
  return useEventStore((state) => state);
};

/**
 * 순수 이벤트 관리 상태만 읽는 훅 (읽기 전용)
 */
export const useEventState = () => {
  return useEventStore((state) => ({
    // 이벤트 리스너 관리 상태
    hasErrors: state.eventErrors.size > 0,
    errorCount: state.eventErrors.size,
    listenerCount: state.activeListeners.size,

    // 디버깅 정보
    listenerInfo: state.getListenerInfo(),
  }));
};
