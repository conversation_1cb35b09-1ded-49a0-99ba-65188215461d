import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandHistoryTable({
  client,
  ...props
}: APIRequestType<EstateClient["land"]["history"]> & {
  client: EstateClient;
}) {
  // TODO: Pagination States
  const [numOfRows] = React.useState<number>(props.numOfRows);
  const [pageNo] = React.useState<number>(props.pageNo);
  // TODO: Date States
  const [startDt] = React.useState<string>(props.startDt || "19480501");
  const [endDt] = React.useState<string>(props.endDt || "20161231");

  // query
  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["history"]>
  >({
    queryKey: ["land/history", { ...props, numOfRows, pageNo, startDt, endDt }],
    queryFn: () =>
      client.land.history({ ...props, numOfRows, pageNo, startDt, endDt }),
  });

  // error handling
  if (isError)
    return (
      <div className="w-full flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <div className="w-full flex flex-col">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold text-center">순번</TableHead>
            <TableHead className="font-bold text-center">대장</TableHead>
            <TableHead className="font-bold text-center">
              토지 이동 사유
            </TableHead>
            <TableHead className="font-bold text-center">
              토지 이동 일자
            </TableHead>
            <TableHead className="font-bold text-center">
              토지 이동 말소 일자
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            // If is loading:
            <TableRow>
              <TableCell colSpan={5} className="text-center">
                Loading ...
              </TableCell>
            </TableRow>
          ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
            // If there is result:
            data.result.resultList.map((res, idx) => (
              <TableRow key={`land-history-${idx}`}>
                <TableCell className="text-center">
                  {res.ladMvmnHistSn}
                </TableCell>
                <TableCell className="text-center">
                  {res.regstrSeCodeNm}
                </TableCell>
                <TableCell className="text-center">
                  {res.ladMvmnPrvonshCodeNm}
                </TableCell>
                <TableCell className="text-center">{res.ladMvmnDe}</TableCell>
                <TableCell className="text-center">
                  {res.ladMvmnErsrDe}
                </TableCell>
              </TableRow>
            ))
          ) : (
            // If there is no result:
            <TableRow>
              <TableCell colSpan={5} className="text-center">
                No Data
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      {/* TODO: Pagination */}
      <span className="text-sm text-right">
        Total Count:{" "}
        {(typeof data?.result !== "string" &&
          data?.result.pageInfo?.totalCount) ||
          "0"}
      </span>
    </div>
  );
}
