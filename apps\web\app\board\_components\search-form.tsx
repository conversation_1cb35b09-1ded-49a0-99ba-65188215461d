"use client";

import { <PERSON><PERSON> } from "@geon-ui/react/primitives/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@geon-ui/react/primitives/form";
import { Input } from "@geon-ui/react/primitives/input";
import { Search } from "lucide-react";
import React from "react";
import { useForm } from "react-hook-form";

export type SearchOption = {
  label: string; // selectbox에 보이는 텍스트
  value: string; // 실제 params에 들어갈 key
};

interface DynamicSearchFormProps<T extends Record<string, any>> {
  options: SearchOption[]; // 검색 조건 리스트
  params: T; // 현재 검색 파라미터
  setParams: React.Dispatch<React.SetStateAction<T>>; // 상태 변경 함수
  defaultField?: string; // 초기 선택 필드 (없으면 options[0].value 사용)
}

export default function SearchForm<T extends Record<string, any>>({
  options,
  params,
  setParams,
  defaultField,
}: DynamicSearchFormProps<T>) {
  const [selectedField, setSelectedField] = React.useState<string>(
    defaultField || options[0]?.value || "",
  );

  const form = useForm<{ keyword: string }>({
    defaultValues: { keyword: "" },
  });

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => {
          const newParams = Object.keys(params).reduce(
            (acc, key) => {
              acc[key] = "";
              return acc;
            },
            {} as Record<string, any>,
          );

          setParams({
            ...newParams,
            [selectedField]: data.keyword,
          } as T);
        })}
        className="flex items-end gap-4 rounded-lg bg-white p-4 shadow"
      >
        {/* 검색조건 selectbox (200px 고정) */}
        <FormItem className="w-[200px]">
          <FormControl>
            <select
              value={selectedField}
              onChange={(e) => setSelectedField(e.target.value)}
              className="w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {options.map((opt) => (
                <option key={opt.value} value={opt.value}>
                  {opt.label}
                </option>
              ))}
            </select>
          </FormControl>
        </FormItem>

        {/* 검색어 입력 (나머지 공간 차지) */}
        {selectedField && (
          <FormField
            control={form.control}
            name="keyword"
            render={({ field }) => (
              <FormItem className="max-w-[500px] flex-1">
                <FormControl>
                  <Input
                    {...field}
                    placeholder={`${selectedField} 입력`}
                    className="w-full"
                  />
                </FormControl>
              </FormItem>
            )}
          />
        )}

        {/* 검색 버튼 */}
        <Button
          type="submit"
          className="rounded-md bg-blue-600 px-6 py-2 text-white hover:bg-blue-700"
        >
          <Search className="mr-2 h-4 w-4" />
          검색
        </Button>
      </form>
    </Form>
  );
}
