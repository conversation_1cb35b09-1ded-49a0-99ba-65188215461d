import { NextRequest, NextResponse } from "next/server";

// Mock 도로 데이터
const MOCK_ROAD_DATA = [
  {
    gid: 1001,
    name: "무안읍 중앙로",
    divi: "01", // 고속도로
    updtDt: "2024-01-15",
    length: 2.5,
    width: 15,
    surface: "아스팔트",
    adminDistrict: "MUAN",
    roadGrade: "city",
  },
  {
    gid: 1002,
    name: "삼향읍 번영로",
    divi: "02", // 국도
    updtDt: "2024-02-20",
    length: 4.2,
    width: 20,
    surface: "콘크리트",
    adminDistrict: "SAMHYANG",
    roadGrade: "national",
  },
  {
    gid: 1003,
    name: "몽탄면 농촌로",
    divi: "04", // 시군도
    updtDt: "2024-03-10",
    length: 1.8,
    width: 8,
    surface: "아스팔트",
    adminDistrict: "MONGTAN",
    roadGrade: "local",
  },
  {
    gid: 1004,
    name: "청계면 산업로",
    divi: "03", // 지방도
    updtDt: "2024-01-25",
    length: 3.1,
    width: 12,
    surface: "아스팔트",
    adminDistrict: "CHEONGYE",
    roadGrade: "local",
  },
  {
    gid: 1005,
    name: "현경면 해안로",
    divi: "05", // 기타도로
    updtDt: "2024-02-14",
    length: 5.6,
    width: 10,
    surface: "아스팔트",
    adminDistrict: "HYEONGYEONG",
    roadGrade: "city",
  },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 검색 파라미터 추출
    const gid = searchParams.get("gid");
    const name = searchParams.get("name");
    const divi = searchParams.get("divi");
    const adminDistrict = searchParams.get("adminDistrict");
    const roadGrade = searchParams.get("roadGrade");
    const updtDtFrom = searchParams.get("updtDtFrom");
    const updtDtTo = searchParams.get("updtDtTo");
    const spatialSearchParam = searchParams.get("spatialSearch");

    // 데이터 필터링
    let filteredData = MOCK_ROAD_DATA;

    // 도형번호 검색
    if (gid) {
      const gidNum = parseInt(gid);
      if (!isNaN(gidNum)) {
        filteredData = filteredData.filter((road) => road.gid === gidNum);
      }
    }

    // 이름 검색 (부분 일치)
    if (name) {
      filteredData = filteredData.filter((road) =>
        road.name.toLowerCase().includes(name.toLowerCase()),
      );
    }

    // 구분 검색
    if (divi) {
      filteredData = filteredData.filter((road) => road.divi === divi);
    }

    // 행정구역 검색
    if (adminDistrict) {
      filteredData = filteredData.filter(
        (road) => road.adminDistrict === adminDistrict,
      );
    }

    // 도로등급 검색
    if (roadGrade) {
      filteredData = filteredData.filter(
        (road) => road.roadGrade === roadGrade,
      );
    }

    // 수정일시 범위 검색
    if (updtDtFrom || updtDtTo) {
      filteredData = filteredData.filter((road) => {
        const roadDate = new Date(road.updtDt);
        const fromDate = updtDtFrom ? new Date(updtDtFrom) : null;
        const toDate = updtDtTo ? new Date(updtDtTo) : null;

        if (fromDate && roadDate < fromDate) return false;
        if (toDate && roadDate > toDate) return false;
        return true;
      });
    }

    // 공간 검색 처리 (Mock)
    if (spatialSearchParam) {
      try {
        const spatialSearch = JSON.parse(spatialSearchParam);
        console.log("공간 검색 파라미터:", spatialSearch);

        // 실제로는 PostGIS 등을 사용한 공간 쿼리 수행
        // 여기서는 Mock으로 일부 데이터만 필터링
        if (spatialSearch.type === "point") {
          // 점 검색: 특정 지역 근처의 도로만 반환
          filteredData = filteredData.slice(0, 2);
        } else if (spatialSearch.type === "polygon") {
          // 면 검색: 다각형 내부의 도로만 반환
          filteredData = filteredData.slice(0, 3);
        }
      } catch (error) {
        console.error("공간 검색 파라미터 파싱 오류:", error);
      }
    }

    // 응답 데이터 구성
    const responseData = filteredData.map((road) => ({
      gid: road.gid,
      name: road.name,
      divi: getDiviName(road.divi),
      updtDt: road.updtDt,
      length: `${road.length}km`,
      width: `${road.width}m`,
      surface: road.surface,
      adminDistrict: getAdminDistrictName(road.adminDistrict),
      roadGrade: getRoadGradeName(road.roadGrade),
    }));

    return NextResponse.json({
      data: responseData,
      totalCount: responseData.length,
      message: `${responseData.length}건의 도로 정보를 찾았습니다`,
    });
  } catch (error) {
    console.error("도로 검색 오류:", error);
    return NextResponse.json(
      { error: "도로 검색 중 오류가 발생했습니다" },
      { status: 500 },
    );
  }
}

// 구분 코드를 이름으로 변환
function getDiviName(code: string): string {
  const diviMap: Record<string, string> = {
    "01": "고속도로",
    "02": "국도",
    "03": "지방도",
    "04": "시군도",
    "05": "기타도로",
  };
  return diviMap[code] || code;
}

// 행정구역 코드를 이름으로 변환
function getAdminDistrictName(code: string): string {
  const districtMap: Record<string, string> = {
    MUAN: "무안읍",
    SAMHYANG: "삼향읍",
    MONGTAN: "몽탄면",
    CHEONGYE: "청계면",
    HYEONGYEONG: "현경면",
    MANIN: "만인면",
    UNMUN: "운문면",
    CHEONGIL: "청일면",
    HAENAM: "해남면",
    ILLO: "일로읍",
    DOCSAN: "도산면",
  };
  return districtMap[code] || code;
}

// 도로등급을 이름으로 변환
function getRoadGradeName(grade: string): string {
  const gradeMap: Record<string, string> = {
    highway: "고속도로",
    national: "국도",
    local: "지방도",
    city: "시군도",
  };
  return gradeMap[grade] || grade;
}
