import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { InnerSidebar, OuterSidebar } from "./_components";
import {
  MapInnerSidebarProvider,
  MapSidebarProvider,
} from "./_contexts/sidebar";

export default function MapLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <MapSidebarProvider>
        <OuterSidebar />
        <MapInnerSidebarProvider>
          <InnerSidebar />
          <SidebarInset className="overflow-hidden">{children}</SidebarInset>
        </MapInnerSidebarProvider>
      </MapSidebarProvider>
    </SidebarProvider>
  );
}
