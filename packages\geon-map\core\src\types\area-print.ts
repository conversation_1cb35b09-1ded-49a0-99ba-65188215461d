export type PaperSize =
  | "A0"
  | "A1"
  | "A2"
  | "A3"
  | "A4"
  | "A0-L"
  | "A1-L"
  | "A2-L"
  | "A3-L"
  | "A4-L";

export interface PaperDimensions {
  width: number; // mm 단위
  height: number; // mm 단위
  dpi?: number; // 기본값: 300
  label?: string; // 표시용 라벨
}

export interface AreaPrintOptions {
  /** 프린트 용지 크기 */
  paperSize?: PaperSize;
  /** 프린트할 요소 또는 선택자 */
  element?: HTMLElement | string;
  /** 프린트 페이지 제목 */
  title?: string;
  /** 프린트 시 스타일 포함 여부 */
  includeStyles?: boolean;
  /** 커스텀 CSS 스타일 */
  customStyles?: string;
}

// Canvas 인쇄 옵션 타입 정의
export interface PrintCanvasOptions {
  width?: number;
  height?: number;
  quality?: number;
  paperSize?: PaperSize;
}

// Image 인쇄 옵션 타입 정의
export interface PrintImageOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  pixelRatio?: number;
  paperSize?: PaperSize;
}

// 용지 크기별 치수 정의 (mm 단위) - 가로 버전 추가
export const PAPER_SIZES: Record<PaperSize, PaperDimensions> = {
  // 세로 방향 (Portrait)
  A0: { width: 841, height: 1189, label: "세로" },
  A1: { width: 594, height: 841, label: "세로" },
  A2: { width: 420, height: 594, label: "세로" },
  A3: { width: 297, height: 420, label: "세로" },
  A4: { width: 210, height: 297, label: "세로" },

  // 가로 방향 (Landscape)
  "A0-L": { width: 1189, height: 841, label: "가로" },
  "A1-L": { width: 841, height: 594, label: "가로" },
  "A2-L": { width: 594, height: 420, label: "가로" },
  "A3-L": { width: 420, height: 297, label: "가로" },
  "A4-L": { width: 297, height: 210, label: "가로" },
};
