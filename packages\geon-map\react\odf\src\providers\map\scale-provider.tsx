"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";

/**
 * ScaleProvider 설정 옵션
 */
export interface ScaleProviderOptions {
  /** Scale Control 초기화 옵션 */
  scaleOptions?: {
    size?: number;
    scaleInput?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 ScaleProvider (Scale Control 설정 전용)
 *
 * Scale Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <ScaleProvider scaleOptions={{ size: 100, scaleInput: false }}>
 *       <ScaleWidget />
 *     </ScaleProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function ScaleProvider({
  children,
  scaleOptions = { size: 100, scaleInput: false },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ScaleProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Scale 설정 등록
    updateConfig({
      scaleOptions,
      autoInitialize,
      onError,
    });
  }, [scaleOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
