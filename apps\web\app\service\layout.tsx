import { ControlsProvider, MapProvider } from "@geon-map/react-odf";
import {
  SidebarInset,
  SidebarProvider,
} from "@geon-ui/react/primitives/sidebar";
import React from "react";

import { InnerSidebar, OuterSidebar, ServiceContextMenu } from "./_components";
import {
  ServiceInnerSidebarProvider,
  ServiceSidebarProvider,
} from "./_contexts/sidebar";

export default function ServiceLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <ServiceSidebarProvider>
        <OuterSidebar />
        <ServiceInnerSidebarProvider>
          <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
            <ControlsProvider
              drawOptions={{
                tools: ["polygon", "point", "lineString"],
                style: {
                  fill: { color: [255, 0, 0, 0.3] },
                  stroke: { color: [255, 0, 0, 1], width: 2 },
                },
              }}
              measureOptions={{
                tools: ["distance", "area"],
                style: {
                  fill: { color: [0, 255, 0, 0.3] },
                  stroke: { color: [0, 255, 0, 1], width: 2 },
                },
              }}
              basemapOptions={{}}
              scaleOptions={{ size: 100 }}
            >
              <InnerSidebar />
              <SidebarInset className="overflow-hidden">
                <ServiceContextMenu>{children}</ServiceContextMenu>
              </SidebarInset>
            </ControlsProvider>
          </MapProvider>
        </ServiceInnerSidebarProvider>
      </ServiceSidebarProvider>
    </SidebarProvider>
  );
}
