"use client";

import { <PERSON>ert, AlertDescription } from "@geon-ui/react/primitives/alert";
import { <PERSON>ert<PERSON>ircle, Loader2, Search } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";

import {
  type ApiSchemaResponse,
  buildSearchSchema,
  type ClientSearchSchema,
  fetchApiSchema,
  getAvailableFields,
  getClientSchema,
  type ServiceSearchSchema,
} from "@/components/dynamic-search";
import {
  type ActiveField,
  type AvailableField,
  DynamicFilterManager,
} from "@/components/dynamic-search/dynamic-filter-manager";
import DynamicResultTable from "@/components/dynamic-search/dynamic-result-table";
import DynamicSearchForm from "@/components/dynamic-search/dynamic-search-form";

import { useFacilitySearch } from "../../_contexts/facility-search";
import type {
  FacilityDetailData,
  FacilityModalMode,
} from "../../_types/facility-detail";
import { CustomHeader } from "./custom-header";
import { FacilityDetailModal } from "./facility-detail-modal";

export type IntegratedResultRow = {
  serial: number | string;
  name: string;
  location?: string;
  status?: string;
  _facilityId: string;
  _facilityType: string;
  [key: string]: any;
};

// 통합 검색 API 함수
async function fetchIntegratedFacilityData(
  facilityIds: string[],
  spatialSearch: any,
  searchParams: Record<string, any> = {},
) {
  // 통합된 단일 API 호출
  const apiParams = new URLSearchParams({
    facilityIds: facilityIds.join(","),
    ...searchParams,
    ...(spatialSearch && { spatialSearch: JSON.stringify(spatialSearch) }),
  });

  const response = await fetch(`/api/dynamic-search/?${apiParams.toString()}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch integrated facility data`);
  }

  const result = await response.json();

  return {
    data: result.data || [],
    totalCount: result.totalCount || 0,
    facilityResults: result.facilityResults || [],
  };
}

export function FacilitySearchContent() {
  const { selectedFacilities, selectedFacilityIds, selectedServiceId } =
    useFacilitySearch();

  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<IntegratedResultRow[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [facilityFilters, setFacilityFilters] = useState<string[]>([]);

  // 동적 스키마 상태
  const [schema, setSchema] = useState<ServiceSearchSchema | null>(null);
  const [schemaLoading, setSchemaLoading] = useState(false);
  const [schemaError, setSchemaError] = useState<string | null>(null);

  // 필터 관리 상태
  const [activeFields, setActiveFields] = useState<string[]>([]);
  const [availableFields, setAvailableFields] = useState<AvailableField[]>([]);
  const [apiSchemaData, setApiSchemaData] = useState<ApiSchemaResponse | null>(
    null,
  );
  const [clientSchemaData, setClientSchemaData] =
    useState<ClientSearchSchema | null>(null);

  // 모달 상태
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<FacilityModalMode>("detail");
  const [selectedFacilityData, setSelectedFacilityData] =
    useState<FacilityDetailData | null>(null);

  // 동적 스키마 로딩
  useEffect(() => {
    async function loadSchema() {
      if (!selectedServiceId) {
        setSchema(null);
        return;
      }

      setSchemaLoading(true);
      setSchemaError(null);

      try {
        console.log("로드 스키마:", selectedServiceId);
        // API 스키마 가져오기
        const apiSchema = await fetchApiSchema(selectedServiceId);

        // 클라이언트 스키마 가져오기
        const clientSchema = getClientSchema(selectedServiceId);

        // 스키마 데이터 저장 (필터 관리용)
        setApiSchemaData(apiSchema);
        setClientSchemaData(clientSchema);

        // 기본 활성 필드 설정
        const defaultFields = clientSchema.defaultFields || [
          "name",
          "spatialSearch",
        ];
        setActiveFields(defaultFields);

        // 스키마 합성 (기본 필드만)
        const finalSchema = await buildSearchSchema(
          apiSchema,
          clientSchema,
          defaultFields,
        );
        setSchema(finalSchema);

        // 사용 가능한 필드 목록 업데이트
        const availableFieldsList = await getAvailableFields(
          apiSchema,
          clientSchema,
          defaultFields,
        );
        setAvailableFields(availableFieldsList);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "스키마 로딩 실패";
        setSchemaError(errorMessage);
        console.error("Schema loading error:", error);
      } finally {
        setSchemaLoading(false);
      }
    }

    loadSchema();
  }, [selectedServiceId]);

  // 필터 추가 핸들러
  const handleAddField = useCallback(
    async (fieldId: string) => {
      if (!apiSchemaData || !clientSchemaData) return;

      const newActiveFields = [...activeFields, fieldId];
      setActiveFields(newActiveFields);

      // 스키마 재생성
      const updatedSchema = await buildSearchSchema(
        apiSchemaData,
        clientSchemaData,
        newActiveFields,
      );
      setSchema(updatedSchema);

      // 사용 가능한 필드 목록 업데이트
      const updatedAvailableFields = await getAvailableFields(
        apiSchemaData,
        clientSchemaData,
        newActiveFields,
      );
      setAvailableFields(updatedAvailableFields);
    },
    [activeFields, apiSchemaData, clientSchemaData],
  );

  // 필터 제거 핸들러
  const handleRemoveField = useCallback(
    async (fieldId: string) => {
      if (!apiSchemaData || !clientSchemaData) return;

      const newActiveFields = activeFields.filter((id) => id !== fieldId);
      setActiveFields(newActiveFields);

      // 스키마 재생성
      const updatedSchema = await buildSearchSchema(
        apiSchemaData,
        clientSchemaData,
        newActiveFields,
      );
      setSchema(updatedSchema);

      // 사용 가능한 필드 목록 업데이트
      const updatedAvailableFields = await getAvailableFields(
        apiSchemaData,
        clientSchemaData,
        newActiveFields,
      );
      setAvailableFields(updatedAvailableFields);
    },
    [activeFields, apiSchemaData, clientSchemaData],
  );

  // 활성 필드 목록 (UI용)
  const activeFieldsForUI: ActiveField[] = useMemo(() => {
    if (!schema || !clientSchemaData) return [];

    return schema.fields.map((field) => ({
      id: field.id,
      label: field.label,
      removable: !clientSchemaData.defaultFields.includes(field.id), // 기본 필드는 제거 불가
    }));
  }, [schema, clientSchemaData]);

  // 필터링된 결과
  const filteredResults = useMemo(() => {
    if (facilityFilters.length === 0) return searchResults;
    return searchResults.filter((row) =>
      facilityFilters.includes(row._facilityId),
    );
  }, [searchResults, facilityFilters]);

  // 통합 검색 실행 함수
  const handleSearch = useCallback(
    async (searchParams: Record<string, any>) => {
      if (selectedFacilityIds.length === 0) {
        alert("검색할 시설물을 선택해주세요");
        return;
      }

      setIsSearching(true);
      try {
        // 공간 검색 파라미터 처리
        const spatialSearchParam = searchParams.spatialSearch
          ? {
              type: searchParams.spatialSearch,
              // TODO: 실제 지도에서 선택된 좌표 정보 추가
            }
          : null;

        // spatialSearch 파라미터 제외한 나머지 파라미터
        const { ...otherParams } = searchParams;

        const result = await fetchIntegratedFacilityData(
          selectedFacilityIds,
          spatialSearchParam,
          otherParams,
        );
        setSearchResults(result.data);
        // 기본적으로 모든 시설물 선택
        const uniqueFacilityIds = Array.from(
          new Set(result.data.map((row: any) => row._facilityId)),
        ) as string[];
        setFacilityFilters(uniqueFacilityIds);
        setHasSearched(true);
      } catch (error) {
        console.error("통합 검색 오류:", error);
        alert("검색 중 오류가 발생했습니다");
      } finally {
        setIsSearching(false);
      }
    },
    [selectedFacilityIds],
  );

  // 위치 조회 함수
  const handleLocationView = useCallback((row: IntegratedResultRow) => {
    console.log("위치 조회:", row);
    // TODO: 지도에서 해당 시설물 위치로 이동하는 로직 구현
    alert(`${row._facilityType}의 위치로 이동합니다`);
  }, []);

  // 상세보기 모달 열기
  const handleDetailView = useCallback((row: IntegratedResultRow) => {
    console.log("상세보기:", row);

    // IntegratedResultRow를 FacilityDetailData로 변환
    const facilityData: FacilityDetailData = {
      facilityId: row._facilityId,
      facilityType: row._facilityType,
      facilityName: row.name,
      registryType: row.registryType || "",
      featureCode: row.featureCode || "",
      managementNumber: row.managementNumber || row.serial?.toString() || "",
      adminDistrict: row.location || "",
      mapSheetNumber: row.mapSheetNumber || "",
      managementAgency: row.managementAgency || "",
      installStartDate: row.installStartDate || "",
      installEndDate: row.installEndDate || "",
      remarks: row.remarks || "",
      areaInfo: row.areaInfo || "",
    };

    setSelectedFacilityData(facilityData);
    setModalMode("detail");
    setModalOpen(true);
  }, []);

  // 등록 모달 열기
  const handleRegisterNew = useCallback(() => {
    setSelectedFacilityData(null);
    setModalMode("register");
    setModalOpen(true);
  }, []);

  // 모달 닫기
  const handleModalClose = useCallback(() => {
    setModalOpen(false);
    setSelectedFacilityData(null);
  }, []);

  // 등록 완료 처리
  const handleRegistered = useCallback((data: FacilityDetailData) => {
    console.log("시설물 등록 완료:", data);
    // TODO: 검색 결과 새로고침 또는 목록에 추가
    alert(`${data.facilityName || "시설물"}이 등록되었습니다.`);
  }, []);

  // 행 클릭 핸들러 (상세보기와 동일)
  const handleRowClick = useCallback(
    (row: IntegratedResultRow) => {
      handleDetailView(row);
    },
    [handleDetailView],
  );

  // 스키마 로딩 중
  if (schemaLoading) {
    return (
      <div className="flex h-full items-center justify-center p-6">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-muted-foreground text-sm">
            검색 설정을 불러오는 중...
          </span>
        </div>
      </div>
    );
  }

  // 스키마 로딩 오류
  if (schemaError) {
    return (
      <div className="flex h-full items-center justify-center p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            검색 설정을 불러올 수 없습니다: {schemaError}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 스키마가 없는 경우
  if (!schema) {
    return (
      <div className="flex h-full items-center justify-center p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            해당 서비스의 검색 설정이 존재하지 않습니다.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // 시설물이 선택되지 않은 경우
  if (selectedFacilities.length === 0) {
    return (
      <div className="flex h-full items-center justify-center p-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            상단에서 시설물을 선택한 후 검색해주세요
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col space-y-2 p-4">
      {/* 검색 폼 */}
      <div className="bg-card flex-shrink-0 space-y-4 rounded-lg border p-4">
        {/* 헤더 영역: 제목과 필터 관리 */}
        <div className="flex items-center justify-between">
          <h2 className="text-foreground text-sm font-medium">
            {schema.title}
          </h2>
          <DynamicFilterManager
            activeFields={activeFieldsForUI}
            availableFields={availableFields}
            onAddField={handleAddField}
            onRemoveField={handleRemoveField}
            disabled={isSearching}
            compact={true}
          />
        </div>

        {/* 검색 폼 */}
        <DynamicSearchForm
          schema={schema}
          onSubmit={handleSearch}
          compact={true}
          loading={isSearching}
          collapsible={false}
        />
      </div>
      <CustomHeader />

      {/* 검색 결과 또는 안내 메시지 */}
      <div className="min-h-0 flex-1">
        {!hasSearched ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <Search className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
              <h3 className="mb-2 text-sm font-medium">
                선택된 시설물:{" "}
                {selectedFacilities.map((f) => f.title).join(", ")}
              </h3>
              <p className="text-muted-foreground mb-1 text-sm">
                검색 조건을 입력하고 검색 버튼을 클릭하세요
              </p>
            </div>
          </div>
        ) : (
          <div className="flex h-full flex-col">
            <div className="min-h-0 flex-1">
              <DynamicResultTable
                columns={[
                  {
                    id: "actions",
                    label: "액션",
                    type: "actions",
                    actions: [
                      { id: "location", label: "위치" },
                      { id: "detail", label: "상세" },
                    ],
                  },
                  ...schema.result.columns,
                ]}
                rows={filteredResults}
                onAction={(actionId, row) => {
                  if (actionId === "location") {
                    handleLocationView(row);
                  } else if (actionId === "detail") {
                    handleDetailView(row);
                  }
                }}
                onRowClick={handleRowClick}
                searchKey="name"
                searchPlaceholder="검색..."
                isLoading={isSearching}
              />
            </div>
          </div>
        )}
      </div>

      {/* 시설물 상세/등록 모달 */}
      <FacilityDetailModal
        isOpen={modalOpen}
        onClose={handleModalClose}
        mode={modalMode}
        facilityData={selectedFacilityData}
        facilityType={selectedFacilityData?.facilityType}
        onRegistered={handleRegistered}
      />
    </div>
  );
}
