import type { APIResponseType, GeonAnalysisClient } from "@geon-query/model";
import type { APIRequestType } from "@geon-query/model";
import { createContext, useContext } from "react";

import type { LayerFileDownloadContextValue } from "../types";
// API Request Param, Response Result 명시용 타입(geon-query 외부용)
type LayerFileDownloadParam = APIRequestType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;
type LayerFileDownloadResult = APIResponseType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;

// Context 생성
export const LayerFileDownloadContext =
  createContext<LayerFileDownloadContextValue<
    LayerFileDownloadParam,
    LayerFileDownloadResult
  > | null>(null);

// 커스텀 훅
export const useLayerFileDownloadContext = () => {
  const context = useContext(LayerFileDownloadContext);
  if (!context) {
    throw new Error(
      "LayerFileDownload components must be used within LayerFileDownload Provider",
    );
  }
  return context;
};
