import { NextRequest, NextResponse } from "next/server";

import type { ServiceSearchResponse } from "@/components/dynamic-search/types";
import type { ServiceSearchSchema } from "@/components/dynamic-search/types";

//demo data
function makeSchema(serviceId: string): ServiceSearchSchema {
  const fields = [
    {
      id: "year",
      label: "기준 연도",
      type: "select",
      options: [
        { label: "2025년 하반기", value: "2025H2" },
        { label: "2025년 상반기", value: "2025H1" },
      ],
    },
    {
      id: "region",
      label: "행정구역",
      type: "select",
      options: [
        { label: "전체", value: "" },
        { label: "무안읍", value: "muan" },
        { label: "삼향읍", value: "samh" },
      ],
    },
    {
      id: "facility",
      label: "시설물명",
      type: "text",
      placeholder: "시설물명을 입력하세요.",
    },
    { id: "date", label: "준공일자", type: "dateRange" },
    {
      id: "searchMode",
      label: "영역검색",
      type: "buttonGroup",
      buttons: [
        { id: "point", label: "점" },
        { id: "poly", label: "면" },
        { id: "buffer", label: "반경" },
        { id: "free", label: "자유작도" },
      ],
    },
  ] satisfies import("@/components/dynamic-search/types").SearchFieldSchema[];

  const columns = [
    {
      id: "detail",
      label: "상세/위치",
      type: "actions" as const,
      actions: [
        { id: "detail", label: "상세" },
        { id: "locate", label: "위치" },
      ],
    },
    { id: "serial", label: "시설물_일련번호", accessor: "serial" },
    { id: "name", label: "시설_명", accessor: "name" },
    { id: "type", label: "시설물_유형", accessor: "type" },
    { id: "status", label: "상태", accessor: "status" },
    { id: "location", label: "소재지", accessor: "location" },
    { id: "manager", label: "관리기관", accessor: "manager" },
    { id: "phone", label: "연락처", accessor: "phone" },
    { id: "constructionDate", label: "준공일자", accessor: "constructionDate" },
    { id: "budget", label: "예산(만원)", accessor: "budget" },
    { id: "area", label: "면적(㎡)", accessor: "area" },
    { id: "contractor", label: "시공업체", accessor: "contractor" },
    { id: "designer", label: "설계업체", accessor: "designer" },
  ] satisfies import("@/components/dynamic-search/types").ResultColumnSchema[];

  const schema: ServiceSearchSchema = {
    serviceId,
    title: `검색 영역`,
    fields,
    result: { columns },
  };
  return schema;
}

function makeData(serviceId: string, params: Record<string, any>) {
  // 단순 목업 데이터
  const base = [
    {
      serial: 9758,
      name: "안전시설물",
      type: "도로안전시설물",
      status: "정상",
      location: "무안읍 무안로 123",
      manager: "무안군청",
      phone: "************",
      constructionDate: "2023-05-15",
      budget: "15,000",
      area: "2,500",
      contractor: "(주)무안건설",
      designer: "(주)설계종합엔지니어링",
    },
    {
      serial: 9664,
      name: "시설물등록테스트_2025",
      type: "교통시설물",
      status: "공사중",
      location: "삼향읍 삼향로 456",
      manager: "무안군청 건설과",
      phone: "************",
      constructionDate: "2025-03-10",
      budget: "25,000",
      area: "1,800",
      contractor: "(주)대한종합건설",
      designer: "(주)미래설계사무소",
    },
    {
      serial: 9542,
      name: "시설물 등록 테스트1",
      type: "공원시설물",
      status: "정상",
      location: "청계면 청계로 789",
      manager: "무안군 산업건설국",
      phone: "************",
      constructionDate: "2022-11-30",
      budget: "8,500",
      area: "3,200",
      contractor: "(주)청계건설",
      designer: "(주)공원설계사무소",
    },
    {
      serial: 10918,
      name: "감리법인",
      type: "관리시설물",
      status: "점검필요",
      location: "몽탄면 몽탄로 321",
      manager: "무안군 총무과",
      phone: "************",
      constructionDate: "2021-08-20",
      budget: "12,000",
      area: "1,500",
      contractor: "(주)몽탄종합건설",
      designer: "(주)전남설계단",
    },
  ];
  // 간단한 필터
  let rows = base;
  if (params?.facility)
    rows = rows.filter((r) => String(r.name).includes(params.facility));
  return rows;
}

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const serviceId = searchParams.get("serviceId") || "road";
  const params: Record<string, any> = {};
  searchParams.forEach((v, k) => {
    if (k !== "serviceId") params[k] = v;
  });

  const schema = makeSchema(serviceId);
  const data = makeData(serviceId, params);

  const res: ServiceSearchResponse = { schema, data };
  return NextResponse.json(res);
}
