"use client";

import React, { useEffect } from "react";

import { useControlsConfig } from "../../contexts/controls-config-context";

/**
 * OverviewProvider 설정 옵션
 */
export interface OverviewProviderOptions {
  /** Overview Control 초기화 옵션 */
  overviewOptions?: {
    enabled?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 OverviewProvider (Overview Control 설정 전용)
 *
 * Overview Control 설정을 ControlsProvider에 전달하는 Config Provider입니다.
 * 실제 초기화는 ControlsProvider에서 수행됩니다.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ControlsProvider>
 *     <OverviewProvider overviewOptions={{ enabled: true }}>
 *       <OverviewWidget />
 *     </OverviewProvider>
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function OverviewProvider({
  children,
  overviewOptions = { enabled: true },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<OverviewProviderOptions>) {
  const { updateConfig } = useControlsConfig();

  useEffect(() => {
    // Controls Config에 Overview 설정 등록
    updateConfig({
      overviewOptions,
      autoInitialize,
      onError,
    });
  }, [overviewOptions, autoInitialize, onError, updateConfig]);

  return <>{children}</>;
}
