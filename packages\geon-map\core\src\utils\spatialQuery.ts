/**
 * WFS 공간/속성 질의 유틸리티
 * ------------------------------------------------------------
 * - ODF 레이어와 ODF Feature(윈도우 전역 odf)를 사용해 CQL 필터를 구성하고
 *   WFS 서버(WFS_URL)에 POST로 질의를 수행합니다.
 */

import { crtfckey, fetcher, WFS_URL } from "@geon-query/model";

import { ODF, ODF_MAP } from "../types/map";

/* ==============================
   전역(window) 선언 보강
   ============================== */

let odf: ODF;
let odfMap: ODF_MAP;

export const setting = (_odfMap: ODF_MAP, _odf: ODF) => {
  odf = _odf;
  odfMap = _odfMap;
};

/* ==============================
   타입 정의
   ============================== */

/** ODF 레이어 형태 최소 인터페이스 (필요한 메서드만 정의) */
export interface LayerLike {
  getGeometryName(): string;
  getInitialOption(): { params: { layer: string } }; // 예: "workspace:layerName"
}

/** ODF Feature 형태 최소 인터페이스 (필요한 메서드만 정의) */
export interface OdfFeatureLike {
  clone(): OdfFeatureLike;
  getId(): string | number | null | undefined;
  setId(id: string | number): void;
  getGeometry(): { getCoordinates(): any };
  setGeometry(geom: any): void;
  setGeometryName(name: string | undefined): void;
  toGeoJson(): { geometry: { type: string; coordinates?: any } };
  toPolygon(): OdfFeatureLike;
  unset(key: string, silent?: boolean): void;
}

/** 공간 연산자 타입 (CQL Spatial Predicates) */
export type SpatialOperator =
  | "EQUALS"
  | "DISJOINT"
  | "INTERSECTS"
  | "TOUCHES"
  | "CROSSES"
  | "WITHIN"
  | "CONTAINS"
  | "OVERLAPS"
  | "RELATE"
  | "DWITHIN"
  | "BEYOND";

/** 질의 옵션 */
export interface QueryOptions {
  /** CQL where 절(문자열) */
  where?: string;
  /** 단일 공간 피처 */
  feature?: OdfFeatureLike;
  /** 복수 공간 피처(OR로 묶임) */
  features?: OdfFeatureLike[];
  /** 공간 연산자 (기본값: INTERSECTS) */
  spatialOperator?: SpatialOperator;
  /** 결과 최대 건수 (기본값: 10,000) */
  maxFeatures?: number;
  /** 정렬 (예: "attrA+D" / "attrA+A") */
  sortBy?: string;
  /** 좌표계/투영 정보 (odfMap.getProjection().projectGeom에 그대로 전달) */
  proj?: unknown;
}

/** GetFeature 파라미터 페이로드 */
interface GetFeaturePayload {
  outputFormat: "application/json";
  service: "WFS";
  version: "1.0.0";
  request: "GetFeature";
  typeName: string;
  apikey: string;
  crtfckey: string;
  maxFeatures: number;
  sortBy: string;
}

/* ==============================
   내부 유틸
   ============================== */

/** 레이어의 geometry 컬럼명 반환 */
const getGeometryName = (layer: LayerLike): string => {
  // return layer.getProperties().geometryName;
  return layer.getGeometryName();
};

/** "workspace:layer"에서 workspace 추출 */
const findNamespace = (layer: LayerLike): string => {
  return layer.getInitialOption().params.layer.split(":")[0] as string;
};

/** CQL 템플릿 */
const CQL_TPL = `[[spatialOperator]]([[geometryName]], [[cqlCoords]])`;

/** feature를 복사(id 유지) */
const cloneFeature = (feature: OdfFeatureLike): OdfFeatureLike => {
  const clone = feature.clone();
  const id = feature.getId();
  if (id !== undefined && id !== null) clone.setId(id);
  return clone;
};

/**
 * 단일 지오메트리를 멀티 지오메트리로 변환하는 헬퍼
 * - 예) Polygon -> MultiPolygon
 */
const toMulti =
  (geometryType: string) =>
  (feature: OdfFeatureLike): OdfFeatureLike => {
    const clone = cloneFeature(feature);
    const temp = odf.FeatureFactory.produce({
      geometryType,
      coordinates: [clone.getGeometry().getCoordinates()],
    });
    clone.setGeometry(temp.getGeometry());
    return clone;
  };

/** Polygon -> MultiPolygon 변환 */
const polygonToMultiPolygon = toMulti("multipolygon");

/** WKT 좌표 문자열 생성 (Circle -> Polygon, Polygon -> MultiPolygon 보정) */
const getCqlCoords = (featureIn: OdfFeatureLike): string => {
  let feature = featureIn;

  const type = feature.toGeoJson().geometry.type;
  if (type === "Circle") {
    feature = feature.toPolygon();
  }
  if (feature.toGeoJson().geometry.type === "Polygon") {
    feature = polygonToMultiPolygon(feature);
  }
  return odf.FeatureFactory.toWKT(feature).wkt;
};

/** 단일 feature를 CQL 필터로 변환 */
const featureToCqlFilter = (
  layer: LayerLike,
  feature: OdfFeatureLike,
  spatialOperator: SpatialOperator,
): string => {
  return CQL_TPL.replace("[[spatialOperator]]", spatialOperator)
    .replace("[[geometryName]]", getGeometryName(layer))
    .replace("[[cqlCoords]]", getCqlCoords(feature));
};

/** 복수 feature를 OR 로 묶어 CQL 필터 생성 */
const featuresToCqlFilter = (
  layer: LayerLike,
  features: OdfFeatureLike[],
  spatialOperator: SpatialOperator,
): string => {
  const cqlFilters = features.map((f) =>
    featureToCqlFilter(layer, f, spatialOperator),
  );
  return `( ${cqlFilters.join(" OR ")} )`;
};

/** WFS GetFeature 기본 페이로드 구성 */
const makePayload = (
  layer: LayerLike,
  options: QueryOptions,
): GetFeaturePayload => {
  const typeName = layer.getInitialOption().params.layer;

  const payload: GetFeaturePayload = {
    outputFormat: "application/json",
    service: "WFS",
    version: "1.0.0",
    request: "GetFeature",
    typeName,
    apikey: "", // 필요 시 채워짐
    crtfckey,
    maxFeatures: 10000,
    sortBy: "",
  };

  if (options.sortBy) payload.sortBy = options.sortBy;
  if (typeof options.maxFeatures === "number")
    payload.maxFeatures = options.maxFeatures;

  return payload;
};

/** where / feature / features를 AND로 묶어 최종 CQL 생성 */
const makeCqlFilter = (layer: LayerLike, options: QueryOptions): string => {
  const spatialOperator: SpatialOperator =
    options.spatialOperator ?? "INTERSECTS";
  const conditions: string[] = [];

  if (options.where) conditions.push(options.where.trim());
  if (options.feature)
    conditions.push(
      featureToCqlFilter(layer, options.feature, spatialOperator),
    );
  if (options.features && options.features.length > 0)
    conditions.push(
      featuresToCqlFilter(layer, options.features, spatialOperator),
    );

  return conditions.join(" AND ");
};

/** 임시 지오메트리 (geometry 없음 대응용) */
const _tempGeometry = {
  geometry: {
    type: "Point",
    coordinates: [0, 0],
  },
};

/** 피처 좌표계를 투영(project) */
const projectGeom = (
  feature: OdfFeatureLike,
  proj: unknown,
): OdfFeatureLike => {
  odfMap.getProjection().projectGeom(feature, proj);
  return feature;
};

/**
 * GeoJSON Feature -> ODF Feature 변환
 * - geometry 미존재/누락 케이스를 방어적으로 처리
 * - geojson id 를 feature id 로 설정
 */
const geoJsonFeatureToOdfFeature = (
  geoJsonFeature: any,
  proj?: unknown,
): OdfFeatureLike => {
  let feature: OdfFeatureLike;

  // geometry 컬럼이 없는 geojson
  if (geoJsonFeature.geometry_name === undefined) {
    feature = odf.FeatureFactory.fromGeoJson(
      Object.assign({}, geoJsonFeature, _tempGeometry),
    );
    feature.setGeometryName(undefined);
    feature.setGeometry(undefined);
  }
  // geometry 컬럼은 있으나 geometry 값이 null
  else if (
    geoJsonFeature.geometry_name !== undefined &&
    geoJsonFeature.geometry == null
  ) {
    feature = odf.FeatureFactory.fromGeoJson(
      Object.assign({}, geoJsonFeature, _tempGeometry),
    );
    feature.setGeometry(undefined);
  } else {
    feature = odf.FeatureFactory.fromGeoJson(geoJsonFeature);
  }

  if (geoJsonFeature.id !== undefined) {
    feature.setId(geoJsonFeature.id);
  }

  if (proj) projectGeom(feature, proj);
  feature.unset("undefined", true); // 잔여 키 정리

  return feature;
};

/** 객체를 쿼리스트링으로 변환 (원본 호환성 유지: 인코딩 미적용) */
const payloadString = <T extends Record<string, unknown>>(payload: T): string =>
  Object.entries(payload)
    .map(([k, v]) => `${k}=${String(v)}`)
    .join("&");

/** GeoJSON Feature[] -> ODF Feature[] 변환 */
const geoJsonFeaturesToOdfFeatures = (
  geoJsonFeatures: any[],
  proj?: unknown,
): OdfFeatureLike[] => {
  return geoJsonFeatures.map((f) => geoJsonFeatureToOdfFeature(f, proj));
};

/** GeoJSON FeatureCollection (EPSG:5186) 결과 타입 */
export interface GeoJsonFeatureCollection {
  type: "FeatureCollection";
  features: GeoJsonFeature[];
  totalFeatures: number;
  numberMatched: number;
  numberReturned: number;
  timeStamp: string; // ISO8601
  crs: {
    type: string;
    properties: {
      name: string; // 예: "urn:ogc:def:crs:EPSG::5186"
    };
  };
  bbox: [number, number, number, number];
}

/** 단일 Feature */
export interface GeoJsonFeature {
  type: "Feature";
  id: string;
  geometry: {
    type: "MultiPolygon";
    coordinates: number[][][][]; // [폴리곤][링][좌표쌍][x|y]
  };
  geometry_name: string; // 예: "geom"
  properties: {
    ufid: string;
    dynm: string;
    dycd: string;
    shape_area: number;
    shape_len: number;
  };
  bbox: [number, number, number, number];
}
// 반환 타입 정의
export type ExecuteResult<R extends { features: any[] }> = {
  result: R; // 원시 응답 (features 포함)
  odfFeatureList: OdfFeatureLike[]; // 변환된 ODF 피처 배열
};

/**
 * WFS 쿼리를 실행하고 결과를 ODF Feature로 변환하여 반환하는 함수
 *
 * @template R  원시 응답의 타입 (기본값: { features: any[] })
 *
 * @param layer   조회 대상 레이어 (ODF Layer)
 * @param options 쿼리 옵션 (속성/공간 조건 등)
 *
 * @returns Promise<{
 *   result: R;                    // fetcher.post 원시 응답
 *   odfFeatureList: OdfFeatureLike[]; // 변환된 ODF Feature 배열
 * }>
 *
 * 사용 예:
 * ```ts
 * const { result, odfFeatureList } = await execute(layer, {
 *   where: "attr1 = 'A'",
 *   feature,
 *   spatialOperator: "INTERSECTS",
 *   maxFeatures: 500,
 *   sortBy: "attr1+D",
 * });
 *
 * console.log(result);        // 원시 응답
 * console.log(odfFeatureList); // 변환된 Feature 리스트
 * ```
 */
export const execute = async (
  layer: LayerLike,
  options: QueryOptions,
): Promise<{
  result: GeoJsonFeatureCollection;
  odfFeatureList: OdfFeatureLike[];
}> => {
  try {
    const cql_filter = makeCqlFilter(layer, options);
    const _namespace = findNamespace(layer); // 필요시 사용
    console.log("_namespace", _namespace);
    const payload = makePayload(layer, options);
    const url = `${WFS_URL}?${payloadString(payload as any)}`;

    // 원시 응답을 제네릭 R로 받되, features 필드는 반드시 존재
    const result = await fetcher.post<GeoJsonFeatureCollection>(
      url,
      { cql_filter },
      "application/x-www-form-urlencoded",
    );
    return {
      result,
      odfFeatureList: geoJsonFeaturesToOdfFeatures(
        result.features,
        options.proj,
      ),
    };
  } catch (e) {
    console.log("layer", layer);
    console.error(e);
    throw e;
  }
};
