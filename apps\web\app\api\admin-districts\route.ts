import { NextRequest, NextResponse } from "next/server";

// 무안군 행정구역 데이터
const ADMIN_DISTRICTS = [
  { code: "<PERSON><PERSON><PERSON>", name: "무안읍", type: "읍", order: 1 },
  { code: "SAMHYANG", name: "삼향읍", type: "읍", order: 2 },
  { code: "<PERSON><PERSON><PERSON>", name: "일로읍", type: "읍", order: 3 },
  { code: "MONGTAN", name: "몽탄면", type: "면", order: 4 },
  { code: "CHEONGY<PERSON>", name: "청계면", type: "면", order: 5 },
  { code: "HYEONGYEONG", name: "현경면", type: "면", order: 6 },
  { code: "MANIN", name: "만인면", type: "면", order: 7 },
  { code: "UNMUN", name: "운문면", type: "면", order: 8 },
  { code: "CHEONGIL", name: "청일면", type: "면", order: 9 },
  { code: "HAENAM", name: "해남면", type: "면", order: 10 },
  { code: "DOCSA<PERSON>", name: "도산면", type: "면", order: 11 },
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type"); // "읍" 또는 "면" 필터

    let districts = ADMIN_DISTRICTS;

    // 타입 필터링
    if (type) {
      districts = districts.filter((district) => district.type === type);
    }

    // API 응답 형태로 변환
    const result = districts
      .sort((a, b) => a.order - b.order)
      .map((district) => ({
        value: district.code,
        label: district.name,
        type: district.type,
      }));

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching admin districts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}

// 특정 행정구역 상세 정보 조회
export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json();

    const district = ADMIN_DISTRICTS.find((d) => d.code === code);

    if (!district) {
      return NextResponse.json(
        { error: `Admin district not found: ${code}` },
        { status: 404 },
      );
    }

    // 상세 정보 (실제로는 더 많은 정보가 포함될 수 있음)
    const detail = {
      ...district,
      fullName: `무안군 ${district.name}`,
      // 실제로는 면적, 인구, 좌표 등의 추가 정보
      area: Math.floor(Math.random() * 50) + 10, // Mock 면적(km²)
      population: Math.floor(Math.random() * 10000) + 1000, // Mock 인구
      coordinates: {
        lat: 34.9 + Math.random() * 0.2,
        lng: 126.4 + Math.random() * 0.3,
      },
    };

    return NextResponse.json(detail);
  } catch (error) {
    console.error("Error fetching admin district detail:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
