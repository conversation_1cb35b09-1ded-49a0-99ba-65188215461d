import type { LayerFactory } from "@geon-map/core";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

import type { Layer } from "../types/layer-types";
import { logger } from "./middleware/logger";

// 레이어 상태 인터페이스
export interface LayerState {
  // 레이어 목록
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;

  // LayerFactory 인스턴스
  layerFactory: LayerFactory | null;

  // 액션들
  addLayer: (layer: Layer) => void;
  removeLayer: (layerId: string) => void;
  updateLayer: (id: string, updates: Partial<Layer>) => void;
  setLayers: (layers: Layer[]) => void;
  toggleLayerVisibility: (layerId: string) => void;
  setLayerFilter: (id: string, filter: string) => void;
  setSelectedLayer: (layerId: string) => void;
  toggleGroup: (groupId: string) => void;
  clearLayers: () => void;

  // LayerFactory 관리
  setLayerFactory: (factory: LayerFactory | null) => void;

  // 🆕 Draw/Measure/Clear 레이어 전용 헬퍼 메서드
  getLayerByType: (type: "draw" | "measure" | "clear") => Layer | null;
  getLayerById: (layerId: string) => Layer | null;
  getLayerTypeById: (layerId: string) => "draw" | "measure" | "clear" | "other";
  addDrawLayer: (
    drawLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
  addMeasureLayer: (
    measureLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
  addClearLayer: (
    clearLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;
}

// 초기 상태
const initialState: LayerState = {
  layers: [],
  selectedLayerId: undefined,
  expandedGroups: new Set<string>(),
  layerFactory: null,

  // 액션들 (팩토리에서 오버라이드됨)
  addLayer: () => {},
  removeLayer: () => {},
  updateLayer: () => {},
  setLayers: () => {},
  toggleLayerVisibility: () => {},
  setLayerFilter: () => {},
  setSelectedLayer: () => {},
  toggleGroup: () => {},
  clearLayers: () => {},
  setLayerFactory: () => {},
  getLayerByType: () => null,
  getLayerById: () => null,
  getLayerTypeById: () => "other",
  addDrawLayer: () => {},
  addMeasureLayer: () => {},
  addClearLayer: () => {},
};

/**
 * 🎯 레이어 스토어 팩토리 함수 (다중 지도 인스턴스 지원)
 */
export const createLayerStore = (initialOptions: Partial<LayerState> = {}) => {
  const mergedInitialState = {
    ...initialState,
    ...initialOptions,
  };

  return create<LayerState>()(
    devtools(
      logger(
        (set, get) => ({
          ...mergedInitialState,

          // 액션 구현
          addLayer: (layer: Layer) =>
            set(
              (state) => ({
                layers: [...state.layers, layer],
              }),
              false,
              "addLayer",
            ),

          removeLayer: (layerId: string) =>
            set(
              (state) => ({
                layers: state.layers.filter((layer) => layer.id !== layerId),
                selectedLayerId:
                  state.selectedLayerId === layerId
                    ? undefined
                    : state.selectedLayerId,
              }),
              false,
              "removeLayer",
            ),

          updateLayer: (id: string, updates: Partial<Layer>) =>
            set(
              (state) => ({
                layers: state.layers.map((layer) =>
                  layer.id === id ? { ...layer, ...updates } : layer,
                ),
              }),
              false,
              "updateLayer",
            ),

          setLayers: (layers: Layer[]) =>
            set(
              () => ({
                layers,
              }),
              false,
              "setLayers",
            ),
          toggleLayerVisibility: (layerId: string) =>
            set(
              (state) => ({
                layers: state.layers.map((layer) =>
                  layer.id === layerId
                    ? { ...layer, visible: !layer.visible }
                    : layer,
                ),
              }),
              false,
              "toggleLayerVisibility",
            ),

          setLayerFilter: (id: string, filter: string) =>
            set(
              (state) => ({
                layers: state.layers.map((layer) =>
                  layer.id === id ? { ...layer, filter } : layer,
                ),
              }),
              false,
              "setLayerFilter",
            ),

          setSelectedLayer: (layerId: string) =>
            set(
              () => ({
                selectedLayerId: layerId,
              }),
              false,
              "setSelectedLayer",
            ),

          toggleGroup: (groupId: string) =>
            set(
              (state) => {
                const newExpandedGroups = new Set(state.expandedGroups);
                if (newExpandedGroups.has(groupId)) {
                  newExpandedGroups.delete(groupId);
                } else {
                  newExpandedGroups.add(groupId);
                }
                return {
                  expandedGroups: newExpandedGroups,
                };
              },
              false,
              "toggleGroup",
            ),

          clearLayers: () =>
            set(
              () => ({
                layers: [],
                selectedLayerId: undefined,
                expandedGroups: new Set(),
              }),
              false,
              "clearLayers",
            ),

          // 🆕 Draw/Measure/Clear 레이어 전용 헬퍼 메서드 구현
          getLayerByType: (type: "draw" | "measure" | "clear") => {
            const state = get();
            return state.layers.find((layer) => layer.type === type) || null;
          },

          getLayerById: (layerId: string) => {
            const state = get();
            return state.layers.find((layer) => layer.id === layerId) || null;
          },

          getLayerTypeById: (layerId: string) => {
            const state = get();
            const layer = state.layers.find((layer) => layer.id === layerId);
            if (!layer) return "other";

            if (["draw", "measure", "clear"].includes(layer.type)) {
              return layer.type as "draw" | "measure" | "clear";
            }
            return "other";
          },

          addDrawLayer: (
            drawLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Draw Layer", visible = true } = options;
            const layer: Layer = {
              id: `draw_${Date.now()}`,
              name,
              type: "draw",
              visible,
              zIndex: 1000, // Draw 레이어는 높은 우선순위
              odfLayer: drawLayer,
              params: {
                drawOptions: {}, // 필요시 그리기 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "draw"),
                  layer,
                ], // 기존 draw 레이어 교체
              }),
              false,
              "addDrawLayer",
            );
          },

          addMeasureLayer: (
            measureLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Measure Layer", visible = true } = options;
            const layer: Layer = {
              id: `measure_${Date.now()}`,
              name,
              type: "measure",
              visible,
              zIndex: 1001, // Measure 레이어는 Draw보다 높은 우선순위
              odfLayer: measureLayer,
              params: {
                measureOptions: {}, // 필요시 측정 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "measure"),
                  layer,
                ], // 기존 measure 레이어 교체
              }),
              false,
              "addMeasureLayer",
            );
          },

          addClearLayer: (
            clearLayer: any,
            options: { name?: string; visible?: boolean } = {},
          ) => {
            const { name = "Clear Layer", visible = true } = options;
            const layer: Layer = {
              id: `clear_${Date.now()}`,
              name,
              type: "clear",
              visible,
              zIndex: 1002, // Clear 레이어는 가장 높은 우선순위
              odfLayer: clearLayer,
              params: {
                clearOptions: {}, // 필요시 정리 옵션 저장
              },
            };

            set(
              (state) => ({
                layers: [
                  ...state.layers.filter((l) => l.type !== "clear"),
                  layer,
                ], // 기존 clear 레이어 교체
              }),
              false,
              "addClearLayer",
            );
          },

          // LayerFactory 설정
          setLayerFactory: (factory: LayerFactory | null) =>
            set({ layerFactory: factory }, false, "setLayerFactory"),
        }),
        "layer-store",
      ),
      {
        name: "layer-store",
      },
    ),
  );
};

/**
 * 🎯 전역 레이어 스토어 (하위 호환성)
 *
 * 기존 코드와의 호환성을 위해 유지합니다.
 * 단일 지도 사용 시에는 이 스토어를 사용합니다.
 */
export const useLayerStore = createLayerStore();
