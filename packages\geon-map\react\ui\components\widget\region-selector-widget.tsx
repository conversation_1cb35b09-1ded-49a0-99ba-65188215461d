"use client";
import { useMap, useProjection } from "@geon-map/react-odf";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@geon-ui/react/primitives/select";
import { cn } from "@geon-ui/react/utils";
import { ChevronRight } from "lucide-react";
import * as React from "react";
import { createContext, useContext, useMemo } from "react";

import { useRegionSelector } from "../../hooks";
import {
  RegionData,
  RegionInfo,
  RegionListData,
  RegionListHandler,
  RegionSelectorContextValue,
  RegionSelectorProps,
  RegionSelectorWidgetProps,
  RegionSelectTriggerProps,
  RegionType,
} from "../../types/region-selector-types";

// ✅ Context 생성
const RegionSelectorContext = createContext<RegionSelectorContextValue | null>(
  null,
);

export const useRegionSelectorContext = () => {
  const context = useContext(RegionSelectorContext);
  if (!context) {
    throw new Error(
      "useRegionSelectorContext는 RegionSelectorProvider 내부에서 사용해야 합니다.",
    );
  }
  return context;
};

// ✅ 지역 선택기 컴포넌트 생성 함수
const createRegionSelector = (
  regionType: RegionType,
  placeholder: string,
  includeSeparator: boolean = false,
) => {
  const Component = React.forwardRef<
    HTMLButtonElement,
    RegionSelectTriggerProps
  >(({ className, style, ...props }, ref) => {
    const {
      selectedRegion,
      regionLists,
      fetchRegionInfo,
      handleRegionChange,
      fetchRegionList,
      actions,
      isLoading,
      useLi,
      setEnableMapSync,
    } = useRegionSelectorContext();

    const handleChange = React.useCallback(
      async (selectedCode: string) => {
        const regionData = await fetchRegionInfo(regionType, selectedCode);
        if (regionData) {
          setEnableMapSync(false);
          const nextConfig = await handleRegionChange({
            regionCode: regionData.code,
            regionType,
            wktPolygon: regionData.wktPolygon,
            regionName: regionData.name,
          });
          // ... 나머지 로직
          if (nextConfig?.next) {
            const parentCode = regionData.code;
            const regionList = await fetchRegionList(
              nextConfig.next,
              parentCode,
            );
            if (regionList) {
              actions.updateRegionList(nextConfig.next, regionList);
            } else {
              console.error(`${regionType} 리스트 조회 실패`);
              setEnableMapSync(true);
              return;
            }
          }
        }
      },
      [regionType, fetchRegionInfo, handleRegionChange, setEnableMapSync],
    );

    const selectedName = useMemo(() => {
      const regionList = regionLists[regionType];
      const selectedCode = selectedRegion[regionType];
      return (
        regionList?.find((item) => item.code === selectedCode)?.name ||
        placeholder
      );
    }, [regionLists[regionType], selectedRegion[regionType]]);

    const items = regionLists[regionType] ?? [];
    const isDisabled = isLoading || items.length === 0;

    // 리를 사용 안하거나 리인 경우 데이터가 없으면 아예 렌더링하지 않음 (구분자도 함께)
    if (regionType === "li" && (!useLi || items.length <= 0)) {
      return null;
    }

    const selectElement = (
      <Select
        value={selectedRegion[regionType] || "선택"}
        onValueChange={handleChange}
        disabled={isDisabled}
      >
        <SelectTrigger
          ref={ref}
          className={cn(
            "bg-white border-none shadow-none [&>svg]:hidden",
            "focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0",
            isDisabled && "opacity-50 cursor-not-allowed",
            className,
          )}
          style={style}
          {...props}
        >
          <SelectValue placeholder={placeholder}>{selectedName}</SelectValue>
        </SelectTrigger>
        <SelectContent className="max-h-[200px] overflow-y-auto">
          {items.map((item: RegionInfo) => (
            <SelectItem key={`${regionType}_${item.code}`} value={item.code}>
              {item.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );

    // 구분자 포함 여부에 따라 렌더링
    if (includeSeparator) {
      return (
        <>
          <RegionSeparator />
          {selectElement}
        </>
      );
    }

    return selectElement;
  });

  // displayName 설정
  Component.displayName = `RegionSelector(${regionType})`;

  return Component;
};

// ✅ 각 지역 선택기 컴포넌트
export const SidoSelector = createRegionSelector("sido", "시도 선택", false);
SidoSelector.displayName = "SidoSelector";

export const SigunguSelector = createRegionSelector(
  "sigungu",
  "시군구 선택",
  true,
);
SigunguSelector.displayName = "SigunguSelector";

export const EupmyeondongSelector = createRegionSelector(
  "eupmyeondong",
  "읍면동 선택",
  true,
);
EupmyeondongSelector.displayName = "EupmyeondongSelector";

export const LiSelector = createRegionSelector("li", "리 선택", true);
LiSelector.displayName = "LiSelector";

// ✅ 구분자 컴포넌트
const RegionSeparator: React.FC = () => (
  <div className="flex items-center justify-center">
    <ChevronRight className="w-4 h-4 text-gray-400" />
  </div>
);

// ✅ 메인 지역 선택기 컨테이너
export const RegionSelector = React.forwardRef<
  HTMLDivElement,
  RegionSelectorProps
>(
  (
    {
      className,
      children,
      fetchRegionInfo,
      fetchRegionList,
      fetchPnu,
      useLi = false,
      ...props
    },
    ref,
  ) => {
    const regionSelectorHook = useRegionSelector();
    const { unprojectPoint } = useProjection();
    // 🎯 성능 최적화: useMap의 center 구독으로 moveend 대체
    const { center } = useMap();
    const [enableMapSync, setEnableMapSync] = React.useState(true);
    const {
      regionLists,
      selectedRegion,
      isLoading,
      error,
      actions,
      handleRegionChange,
      getParentRegionType,
    } = regionSelectorHook;
    const actionsRef = React.useRef(actions);
    const enableMapSyncRef = React.useRef(true);
    // enableMapSync 상태 변경시 ref 동기화
    React.useEffect(() => {
      enableMapSyncRef.current = enableMapSync;
      actionsRef.current = actions;
    }, [enableMapSync, actions]);

    const handleUpdateRegionByLonLatRef = React.useRef<
      ((lonLat: [number, number]) => Promise<void>) | null
    >(null);

    // 좌표 기반 지역 업데이트 함수를 ref에 저장
    handleUpdateRegionByLonLatRef.current = async (
      lonLat: [number, number],
    ) => {
      try {
        // 1. 좌표로부터 지역코드 조회
        const center = unprojectPoint(lonLat, "4326");
        const regionCodes = await fetchPnu(center);
        if (!regionCodes) {
          console.error("좌표 기반 지역코드 조회 실패");
          return;
        }

        // 2. 공통 함수 사용하여 지역 데이터 로드 및 초기화
        await loadAndInitializeRegions(
          regionCodes,
          fetchRegionList,
          getParentRegionType,
          actions.initialize,
          useLi,
          regionLists,
        );
      } catch (error) {
        console.error("좌표 기반 지역 업데이트 실패:", error);
      }
    };

    React.useEffect(() => {
      if (!center || center.length !== 2) return;

      const timeoutId = setTimeout(() => {
        const centerLonLat = center;
        if (centerLonLat && handleUpdateRegionByLonLatRef.current) {
          if (enableMapSyncRef.current) {
            actions.clearMapHighlight();
            handleUpdateRegionByLonLatRef.current(centerLonLat);
          } else {
            setEnableMapSync(true);
          }
        }
      }, 100); // 100ms 디바운싱

      return () => clearTimeout(timeoutId);
    }, [center]); // actions 의존성 제거하고 ref 사용

    const contextValue = useMemo<RegionSelectorContextValue>(
      () => ({
        // 상태
        regionLists,
        selectedRegion,
        isLoading,
        error,

        // 액션
        actions,
        handleRegionChange,

        // API 함수들
        fetchRegionInfo,
        fetchRegionList,
        fetchPnu,

        // 설정값들
        useLi,
        enableMapSync,
        setEnableMapSync,

        // 유틸 함수
        getParentRegionType,
      }),
      [
        regionLists,
        selectedRegion,
        isLoading,
        error,
        actions,
        handleRegionChange,
        fetchRegionInfo,
        fetchRegionList,
        fetchPnu,
        useLi,
        enableMapSync,
        setEnableMapSync,
        getParentRegionType,
      ],
    );
    return (
      <RegionSelectorContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn(
            "flex items-center gap-2 bg-white rounded-md p-0.5",
            className,
          )}
          {...props}
        >
          {children}
        </div>
      </RegionSelectorContext.Provider>
    );
  },
);
RegionSelector.displayName = "RegionSelector";

// ✅ 완성된 위젯 컴포넌트
export const RegionSelectorWidget: React.FC<RegionSelectorWidgetProps> = ({
  useLi = false,
  ...props
}) => {
  return (
    <RegionSelector useLi={useLi} {...props}>
      <SidoSelector />
      <SigunguSelector />
      <EupmyeondongSelector />
      <LiSelector />
    </RegionSelector>
  );
};
/**
 * 지역 데이터를 로드하고 초기화하는 공통 함수
 * @param regionCodes - 지역 코드 객체 (sido, sigungu, eupmyeondong, li)
 * @param fetchRegionList - 지역 리스트를 가져오는 함수
 * @param getParentRegionType - 상위 지역 타입을 반환하는 함수
 * @param initializeRegions - 지역 데이터를 초기화하는 함수
 * @param useLi - 리 단위 사용 여부
 * @param currentRegionLists - 현재 지역 목록 데이터 (최적화용)
 * @returns Promise<boolean | null> - 성공 시 true, 실패 시 null
 *
 */
const loadAndInitializeRegions = async (
  regionCodes: RegionData,
  fetchRegionList: RegionListHandler,
  getParentRegionType: (regionType: RegionType) => RegionType | undefined,
  initializeRegions: (
    currentRegionCodes: RegionData | null,
    regionListsData: RegionListData,
    useLi?: boolean,
  ) => void,
  useLi: boolean,
  currentRegionLists?: RegionListData,
): Promise<boolean | null> => {
  try {
    // 1. 지역 리스트 데이터 구조 초기화
    const regionListsData: RegionListData = {
      sido: [],
      sigungu: [],
      eupmyeondong: [],
      li: [],
    };

    // 2. 각 지역 타입별로 리스트 데이터 조회
    for (const [regionType, regionCode] of Object.entries(regionCodes)) {
      if (!regionCode) continue; // 지역 코드가 없으면 건너뛰기
      if (regionType === "li" && !useLi) continue;
      const regionKey = regionType as RegionType;

      // 3. 상위 지역 타입 및 코드 확인
      const parentRegionType = getParentRegionType(regionKey);
      const parentCode = parentRegionType
        ? regionCodes[parentRegionType]
        : undefined;

      // 4. 기존 목록이 있으면 재사용, 없으면 새로 조회
      if (
        currentRegionLists?.[regionKey]?.length &&
        currentRegionLists[regionKey].find(
          (item) => item.code === regionCodes[regionKey],
        ) !== undefined
      ) {
        regionListsData[regionKey] = currentRegionLists[regionKey];
        continue;
      }

      // 5. 해당 지역 타입의 리스트 새로 조회
      const regionList = await fetchRegionList(regionKey, parentCode);

      if (regionList) {
        // 성공적으로 조회된 경우 데이터 저장
        regionListsData[regionKey] = regionList;
      } else {
        // 조회 실패 시 에러 로그 출력 후 함수 종료
        console.error(`${regionType} 리스트 조회 실패`);
        return null; // 실패시 null 반환
      }
    }

    // 6. 모든 지역 데이터가 성공적으로 로드되면 초기화 실행
    initializeRegions(regionCodes, regionListsData, useLi);

    return true; // 성공시 true 반환
  } catch (error) {
    console.error("지역 데이터 로드 중 오류 발생:", error);
    return null; // 에러 발생시 null 반환
  }
};
