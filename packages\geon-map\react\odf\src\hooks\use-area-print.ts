import {
  AreaPrint,
  type AreaPrintOptions,
  type PaperSize,
  type PrintCanvasOptions,
  type PrintImageOptions,
} from "@geon-map/core";
import { useCallback, useRef } from "react";

export interface UseAreaPrintOptions extends AreaPrintOptions {
  /** 자동으로 프린트 인스턴스를 생성할지 여부 */
  autoCreate?: boolean;
}

export interface UseAreaPrintReturn {
  /** 요소를 프린트합니다 */
  print: (element?: HTMLElement | string) => Promise<void>;
  /** Canvas를 프린트합니다 (Canvas/그래픽 라이브러리 사용시)*/
  printCanvas: (
    canvas: HTMLCanvasElement,
    options?: PrintCanvasOptions,
  ) => Promise<void>;
  /** HTML을 이미지로 변환하여 프린트합니다 (복잡한 CSS 렌더링 이슈 해결시) */
  printAsImage: (
    element: HTMLElement | string,
    options?: PrintImageOptions,
  ) => Promise<void>;
  /** 페이지 전체를 프린트합니다 */
  printPage: () => void;
  /** 🎯 미리보기용 캔버스를 생성합니다 */
  createPreviewCanvas: (
    canvas: HTMLCanvasElement,
    paperSize?: PaperSize,
    maxPreviewSize?: number,
  ) => HTMLCanvasElement;
}

export function useAreaPrint(
  options: UseAreaPrintOptions = {},
): UseAreaPrintReturn {
  const { autoCreate = true, ...printOptions } = options;
  const areaPrintRef = useRef<AreaPrint | null>(null);

  // AreaPrint 인스턴스 생성
  const getAreaPrint = useCallback(() => {
    if (!areaPrintRef.current && autoCreate) {
      areaPrintRef.current = new AreaPrint(printOptions);
    }
    return areaPrintRef.current;
  }, [autoCreate, printOptions]);

  const print = useCallback(
    async (element?: HTMLElement | string, paperSize?: PaperSize) => {
      const areaPrint = getAreaPrint();
      if (!areaPrint) {
        throw new Error("AreaPrint 인스턴스가 생성되지 않았습니다.");
      }
      return areaPrint.print(element, paperSize);
    },
    [getAreaPrint],
  );

  const printCanvas = useCallback(
    async (canvas: HTMLCanvasElement, options?: PrintCanvasOptions) => {
      const areaPrint = getAreaPrint();
      if (!areaPrint) {
        throw new Error("AreaPrint 인스턴스가 생성되지 않았습니다.");
      }
      return areaPrint.printCanvas(canvas, options);
    },
    [getAreaPrint],
  );

  const printAsImage = useCallback(
    async (element: HTMLElement | string, options?: PrintImageOptions) => {
      const areaPrint = getAreaPrint();
      if (!areaPrint) {
        throw new Error("AreaPrint 인스턴스가 생성되지 않았습니다.");
      }
      return areaPrint.printAsImage(element, options);
    },
    [getAreaPrint],
  );

  const printPage = useCallback(() => {
    AreaPrint.printPage();
  }, []);

  // 미리보기용 캔버스 생성 함수
  const createPreviewCanvas = useCallback(
    (
      canvas: HTMLCanvasElement,
      paperSize: PaperSize = "A4",
      maxPreviewSize: number = 800,
    ): HTMLCanvasElement => {
      const areaPrint = getAreaPrint();
      if (!areaPrint) {
        throw new Error("AreaPrint 인스턴스가 생성되지 않았습니다.");
      }

      return areaPrint.createPreviewCanvas(canvas, paperSize, maxPreviewSize);
    },
    [getAreaPrint],
  );

  return {
    print,
    printCanvas,
    printAsImage,
    printPage,
    createPreviewCanvas,
  };
}
