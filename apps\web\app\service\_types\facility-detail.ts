/**
 * 시설물 상세 정보 데이터 타입
 */
export interface FacilityDetailData {
  /** 대장구분 */
  registryType: string;
  /** 지형지물부호 */
  featureCode: string;
  /** 관리번호 */
  managementNumber: string;
  /** 행정구역 */
  adminDistrict: string;
  /** 도엽번호 */
  mapSheetNumber: string;
  /** 관리기관 */
  managementAgency: string;
  /** 설치 시작일 */
  installStartDate: Date | string;
  /** 설치 종료일 */
  installEndDate: Date | string;
  /** 비고 */
  remarks: string;
  /** 영역정보등록 (등록시만 사용) */
  areaInfo?: string;
  /** 시설물 ID */
  facilityId?: string;
  /** 시설물 타입 */
  facilityType?: string;
  /** 시설물 명 */
  facilityName?: string;
}

/**
 * 모달 모드 타입
 */
export type FacilityModalMode = "detail" | "register";

/**
 * 시설물 상세 모달 Props
 */
export interface FacilityDetailModalProps {
  /** 모달 열림 상태 */
  isOpen: boolean;
  /** 모달 닫기 콜백 */
  onClose: () => void;
  /** 모달 모드 (상세보기/등록) */
  mode: FacilityModalMode;
  /** 시설물 상세 데이터 */
  facilityData?: FacilityDetailData;
  /** 시설물 타입 */
  facilityType?: string;
  /** 등록 완료 콜백 */
  onRegistered?: (data: FacilityDetailData) => void;
}

/**
 * 시설물 상세보기 Props
 */
export interface FacilityDetailViewProps {
  /** 시설물 상세 데이터 */
  data: FacilityDetailData;
  /** 로딩 상태 */
  loading?: boolean;
}

/**
 * 시설물 등록 폼 Props
 */
export interface FacilityDetailFormProps {
  /** 초기 데이터 */
  initialData?: Partial<FacilityDetailData>;
  /** 시설물 타입 */
  facilityType?: string;
  /** 제출 콜백 */
  onSubmit: (data: FacilityDetailData) => void;
  /** 취소 콜백 */
  onCancel: () => void;
  /** 로딩 상태 */
  loading?: boolean;
}

/**
 * 다운로드 버튼 Props
 */
export interface FacilityDownloadButtonsProps {
  /** 다운로드할 데이터 */
  data: FacilityDetailData;
  /** 로딩 상태 */
  loading?: boolean;
  /** 비활성 상태 */
  disabled?: boolean;
}

/**
 * 다운로드 파일 형식
 */
export type DownloadFormat = "docs" | "pdf";

/**
 * 다운로드 옵션
 */
export interface DownloadOptions {
  /** 파일 형식 */
  format: DownloadFormat;
  /** 파일명 (확장자 제외) */
  filename?: string;
  /** 추가 메타데이터 */
  metadata?: Record<string, any>;
}
