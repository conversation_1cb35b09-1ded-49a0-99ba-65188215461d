{"name": "web", "version": "0.1.2", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "dev:prod": "cross-env NODE_ENV=production next dev --turbopack --port 3001", "analyze": "cross-env ANALYZE=true next build", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@config/tailwind": "workspace:^", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@geon-map/core": "workspace:^", "@geon-map/react-odf": "workspace:^", "@geon-map/react-ui": "workspace:^", "@geon-query/model": "workspace:^", "@geon-query/react-query": "workspace:*", "@geon-ui/react": "workspace:^", "@hookform/resolvers": "^5.2.1", "@svgr/webpack": "^8.1.0", "@tanstack/react-table": "^8.21.3", "date-fns": "^4.1.0", "lucide-react": "^0.542.0", "next": "^15.5.2", "next-intl": "^4.3.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@config/eslint": "workspace:*", "@config/typescript": "workspace:*", "@next/bundle-analyzer": "^15.5.2", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-links": "^9.1.5", "@storybook/nextjs": "^9.1.5", "@tailwindcss/postcss": "^4.1.13", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^22.18.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "autoprefixer": "^10.4.21", "eslint": "^9.35.0", "eslint-plugin-storybook": "^9.1.5", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "postcss-loader": "^8.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "raw-loader": "^4.0.2", "storybook": "^9.1.5", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}