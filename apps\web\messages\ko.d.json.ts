// This file is auto-generated by next-intl, do not edit directly.
// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments

declare const messages: {
  "estate": {
    "land": {
      "basic": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "대장 구분",
        "lndcgrCodeNm": "지목",
        "lndpclAr": "면적(㎡)",
        "posesnSeCodeNm": "소유 구분",
        "cnrsPsnCo": "소유(공유)인 수(명)",
        "ladFrtlScNm": "축척 구분"
      },
      "useplan": {
        "manageNo": "도면 번호",
        "regstrSeCodeNm": "대장 구분",
        "cnflcAtNm": "저촉 여부",
        "prposAreaDstrcCodeNm": "용도지역지구",
        "lastUpdtDt": "기준 일자"
      }
    },
    "building": {
      "ho": {
        "buldNm": "건물 이름",
        "buldDongNm": "동",
        "buldFloorNm": "층",
        "buldHoNm": "호실",
        "regstrSeCodeNm": "대장 구분",
        "ldaQotaRate": "대지권 비율"
      }
    },
    "registry": {
      "headings": {
        "itgBldGrade": "지능형 건축물 등급",
        "itgBldCert": "지능형 건축물 인증 점수",
        "crtnDay": "생성 일자",
        "naMainBun": "새 주소 본번",
        "platArea": "대지 면적(㎡)",
        "archArea": "건축 면적(㎡)",
        "bcRat": "건폐율(%)",
        "totArea": "연면적(㎡)",
        "vlRatEstmTotArea": "용적률 산정 연면적(㎡)",
        "vlRat": "용적률(%)",
        "mainPurpsCdNm": "주 용도",
        "etcPurps": "기타 용도",
        "hhldCnt": "세대수(세대)",
        "fmlyCnt": "가구수(가구)",
        "mainBldCnt": "주 건축물 수",
        "atchBldCnt": "부속 건축물 수",
        "atchBldArea": "부속 건축물 면적(㎡)",
        "totPkngCnt": "총 주차 수",
        "indrMechUtcnt": "옥내 기계식 대수(대)",
        "indrMechArea": "옥내 기계식 면적(㎡)",
        "oudrMechUtcnt": "옥외 기계식 대수(대)",
        "oudrMechArea": "옥외 기계식 면적(㎡)",
        "indrAutoUtcnt": "옥내 자주식 대수(대)",
        "indrAutoArea": "옥내 자주식 면적(㎡)",
        "oudrAutoUtcnt": "옥외 자주식 대수(대)",
        "oudrAutoArea": "옥외 자주식 면적(㎡)",
        "pmsDay": "허가일",
        "stcnsDay": "착공일",
        "useAprDay": "사용 승인일",
        "pmsnoYear": "허가 번호 년",
        "pmsnoKikCdNm": "허가 번호 기관",
        "pmsnoGbCdNm": "허가 번호 구분",
        "hoCnt": "호수(호)",
        "engrGrade": "에너지 효율 등급",
        "engrRat": "에너지 절감율",
        "engrEpi": "EPI 점수",
        "gnBldGrade": "친환경 건축물 등급",
        "gnBldCert": "친환경 건축물 인증 점수",
        "rnum": "순번",
        "platPlc": "대지 위치",
        "mgmBldrgstPk": "관리 건축물 대장 PK",
        "regstrGbCdNm": "대장 구분",
        "regstrKindCdNm": "대장 종류",
        "lastUpdtDt": "데이터 기준 일자",
        "newOldRegstrGbCdNm": "신구 대장 구분",
        "newPlatPlc": "도로명 대지 위치",
        "bldNm": "건물",
        "splotNm": "특수지",
        "block": "블록",
        "lot": "로트",
        "bylotCnt": "외필지수"
      }
    },
    "price": {
      "ind": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "특수지 구분",
        "stdrYear": "기준연도",
        "stdrMt": "기준월",
        "ladRegstrAr": "토지대장 면적(㎡)",
        "calcPlotAr": "산정 대지 면적(㎡)",
        "buldAllTotAr": "건물 전체 연면적(㎡)",
        "buldCalcTotAr": "건물 산정 연면적(㎡)",
        "housePc": "주택 가격(원)"
      },
      "pclnd": {
        "pnu": "필지 고유번호",
        "ldCodeNm": "법정동",
        "mnnmSlno": "지번",
        "regstrSeCodeNm": "특수지 구분",
        "stdrYear": "기준연도",
        "stdrMt": "기준월",
        "pblntfPclnd": "공시지가(원/㎡)",
        "pblntfDe": "공시일자"
      }
    }
  },
  "pagination": {
    "resultStatus": "총 {totalCount, number} 개 중 {start, number}부터 {end, number}까지 표시 중",
    "pageStatus": "{currentPage, number} / {totalPages, number}",
    "rowsPerPage": "행"
  },
  "copy": {
    "types": {
      "coord": "경위도",
      "juso": "지번 주소",
      "pnu": "PNU"
    },
    "fail": "{type}을(를) 불러올 수 없습니다.",
    "success": "{type}이(가) 복사되었습니다."
  },
  "dialog": {
    "close": "닫기"
  }
};
export default messages;