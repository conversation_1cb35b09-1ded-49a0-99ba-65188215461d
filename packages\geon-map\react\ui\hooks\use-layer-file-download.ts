"use-client";

import type { APIRequestType, GeonAnalysisClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";

export type LayerFileDownloadApiType = "geon";
export type LayerFileDownloadClientType<T extends LayerFileDownloadApiType> =
  T extends "geon" ? GeonAnalysisClient : any;

// 옵션 타입
export interface UseLayerFileDownloadOptions<
  T extends LayerFileDownloadApiType = "geon",
> {
  apiType?: T;
  apiClient?: LayerFileDownloadClientType<T>; // 클라이언트 객체 직접주입
}

// API Request Param 명시용 타입(geon-query 외부용)
type LayerFileDownloadParam = APIRequestType<
  GeonAnalysisClient["fileDownload"]["layerFileDownload"]
>;

/** ### 레이어 파일 다운로드 훅 */
export const useLayerFileDownload = <
  T extends LayerFileDownloadApiType = "geon",
>(
  options: UseLayerFileDownloadOptions<T> = {} as UseLayerFileDownloadOptions<T>,
) => {
  const { apiType = "geon" as T, apiClient } = options;

  // 다운로드 뮤테이션
  const downloadLayerFileMutation = useAppMutation({
    mutationFn: async (params: LayerFileDownloadParam) => {
      // 실제 사용 시점에 체크
      if (!apiClient) {
        throw new Error("useLayerFileDownload: API 클라이언트가 필요합니다.");
      }

      return apiClient.fileDownload.layerFileDownload(params);
    },
  });

  // 위젯용 다운로드 핸들러
  const handleLayerFileDownloadInMemory = async (
    params: LayerFileDownloadParam,
  ) => {
    try {
      const result = await downloadLayerFileMutation.mutateAsync(params);
      return result;
    } catch (error) {
      console.error("Layer 파일 다운로드 오류: ", error);
      return undefined;
    }
  };

  return {
    // 레이어 파일 다운로드 기능
    handleLayerFileDownloadInMemory, // 위젯용
    downloadLayerFile: downloadLayerFileMutation.mutateAsync, // 순수 API

    // 상태
    isLoading: downloadLayerFileMutation.isPending,
    error: downloadLayerFileMutation.error,

    // 유틸리티
    reset: downloadLayerFileMutation.reset,
    apiClient: apiClient,
    apiType,
  };
};
