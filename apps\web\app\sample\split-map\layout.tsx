import { ControlsProvider, MapProvider } from "@geon-map/react-odf";
import React from "react";

export default function SplitMapLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider
      defaultOptions={{
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********], // 무안 기본값
        zoom: 8,
      }}
    >
      <ControlsProvider
        drawOptions={{
          tools: ["polygon", "point", "lineString"],
          style: {
            fill: { color: [255, 0, 0, 0.3] },
            stroke: { color: [255, 0, 0, 1], width: 2 },
          },
        }}
        measureOptions={{
          tools: ["distance", "area"],
          style: {
            fill: { color: [0, 255, 0, 0.3] },
            stroke: { color: [0, 255, 0, 1], width: 2 },
          },
        }}
        basemapOptions={{}}
        scaleOptions={{ size: 100 }}
      >
        {children}
      </ControlsProvider>
    </MapProvider>
  );
}
