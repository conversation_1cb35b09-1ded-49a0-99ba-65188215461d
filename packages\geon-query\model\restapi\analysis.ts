/** @Todo : analysis-type 만들고 import, fetcher import */
import { API_TYPE, apiHelper, BlobResponse } from "../utils/geonAPI";
import { LayerFileDownloadRequest } from "./type/analysis-type";

const type: API_TYPE = "analysis";

// 설정 타입
export interface AnalysisConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

// geon-query 외부에서 createGeonAnalysisClient 객체들의 Request, Response 타입을 사용할 때,
// APIRequestType, APIResponseType 과 함께 사용하는 타입
export type GeonAnalysisClient = ReturnType<typeof createGeonAnalysisClient>;

// 동적 API 클라이언트 생성 함수
export function createGeonAnalysisClient(config: AnalysisConfig = {}) {
  const { baseUrl, crtfckey } = config;
  const api = apiHelper({ type, baseUrl, crtfckey });
  return {
    fileDownload: {
      /** ### 레이어 파일 다운로드 (e.g. .csv, .zip, .geojson, .kml) */
      layerFileDownload: api.post<LayerFileDownloadRequest, BlobResponse>(
        "/layer/file/download",
        "application/json",
        "blob",
      ),
    },
  };
}
